# 输入文件

在 pySWAP 中，您不会直接与经典的 SWAP 配置文件交互，但了解模型使用的输入文件会很有帮助。SWAP 模型依赖于带有自定义扩展名的 ASCII 输入文件来获取模拟参数。这些文件包括：

- [**.swp**](/wiki/input-files/swp-file.md) - 主配置文件。
- [**.crp**](/wiki/input-files/crp-file.md) - 作物生长参数。
- [**.dra**](/wiki/input-files/dra-file.md) - 侧向排水参数。
- [**.bbc**](/wiki/input-files/bbc-file.md) - 底部边界条件设置。

还有用于模拟的逗号分隔值文件：

- [**.met**](/wiki/input-files/met-file.md) - 气象数据（所有年份在一个文件中）。
- **.yyy** - 气象数据（年份的最后 3 位；pySWAP 中未使用）。
- [**.irg**](/wiki/input-files/irg-file.md) - 灌溉数据。

ASCII 文件易于创建和编辑，使数据输入变得简单。脚本可以帮助高效地生成和运行多个模型场景。以下部分提供了更多文档，每个模板文件都解释了可用的参数和开关。这些模板来自 R 包 (SWAP Tools)。
