# 欢迎来到 pyswap 文档

pyswap 是 SWAP 水文模型（4.2 版）的第一个综合 Python 封装。它允许您使用 Python 创建和与这些模型交互，包括 Jupyter Notebook 等交互式工具。pyswap 将 HDF5 集成作为模型的存储和通用交换文件格式，促进与 R 用户的无缝协作，R 用户可以利用 SWAP Tools 等包来处理 SWAP 模型。此外，它还与外部数据库集成，例如用于荷兰天气数据的 KNMI 和 WOFOST 作物参数数据库。

模型变量基于 SWAP 开发人员的原始输入文件、用户手册 (<PERSON><PERSON><PERSON> et al., 2017) 以及 [SWAP 网站](https://www.swap.alterra.nl/)上提供的其他文档。

!!! note

      虽然 pyswap 已经支持核心 SWAP 功能，但仍有很大的改进空间。我们非常鼓励贡献，无论是提出问题还是在 GitHub 上发起讨论。有关如何贡献的更多详细信息，请访问[贡献部分](contributing/index.md)并参与进来！

## 快速链接

<div class="grid cards" markdown>

- :octicons-zap-16:{ .lg .middle } **快速入门**

      ***

      了解如何安装软件包并开始您的第一个模型。

      [:octicons-arrow-right-24: 安装](user-guide/quick-start.md)

- :octicons-checklist-16:{ .lg .middle } **输入验证**

      ***

      pySWAP 使用 Pydantic 和 Pandera 验证框架来确保
      SWAP 模拟顺利运行

      <!-- [:octicons-arrow-right-24: Customization](#) -->

- :fontawesome-brands-markdown:{ .lg .middle } **Markdown 文档**

      ***

      此文档以 Markdown 编写，允许任何人贡献

      [:octicons-arrow-right-24: 查看方法](contributing/index.md)

- :material-scale-balance:{ .lg .middle } **开放软件 - 开放科学**

      ***

      pySWAP 是采用 MIT 许可的开源软件，旨在提高
      建模工作的透明度和可共享性

</div>

## SWAP: 土壤-水-大气-植物模型
