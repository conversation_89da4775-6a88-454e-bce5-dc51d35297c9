# mypy: disable-error-code="call-overload, misc, override, type-arg"

"""模拟的通用设置和 Richards 方程的设置。
:
类：
    GeneralSettings: 模拟的通用设置。.
    RichardsSettings: <PERSON> 方程的设置。
"""

import logging as _logging
from datetime import date as _date
from typing import (
    ClassVar as _ClassVar,
    Literal as _Literal,
)

from pydantic import (
    ConfigDict as _ConfigDict,
    Field as _Field,
    model_validator as _model_validator,
)

from pyswap.core.basemodel import PySWAPBaseModel as _PySWAPBaseModel
from pyswap.core.defaults import (
    BASE_PATH as _BASE_PATH,
    EXTENSIONS as _EXTENSIONS,
    FNAME_OUT as _FNAME_OUT,
)
from pyswap.core.fields import (
    Arrays as _Arrays,
    DayMonth as _DayMonth,
    String as _String,
    StringList as _StringList,
    Subsection as _Subsection,
)
from pyswap.core.valueranges import (
    UNITRANGE as _UNITRANGE,
    YEARRANGE as _YEARRANGE,
)
from pyswap.utils.mixins import (
    SerializableMixin as _SerializableMixin,
    YAMLValidatorMixin as _YAMLValidatorMixin,
)

__all__ = ["GeneralSettings", "RichardsSettings"]

logger = _logging.getLogger(__name__)


class _ExtensionMixin(_PySWAPBaseModel, _SerializableMixin):
    """通过直接赋值和列表处理开关的创建。"""

    swwba: _Literal[1, 0] | None = _Field(default=None, validation_alias="wba")
    swend: _Literal[1, 0] | None = _Field(default=None, validation_alias="end")
    swvap: _Literal[1, 0] | None = _Field(default=None, validation_alias="vap")
    swbal: _Literal[1, 0] | None = _Field(default=None, validation_alias="bal")
    swblc: _Literal[1, 0] | None = _Field(default=None, validation_alias="blc")
    swsba: _Literal[1, 0] | None = _Field(default=None, validation_alias="sba")
    swate: _Literal[1, 0] | None = _Field(default=None, validation_alias="ate")
    swbma: _Literal[1, 0] | None = _Field(default=None, validation_alias="bma")
    swdrf: _Literal[1, 0] | None = _Field(default=None, validation_alias="drf")
    swswb: _Literal[1, 0] | None = _Field(default=None, validation_alias="swb")
    swini: _Literal[1, 0] | None = _Field(default=None, validation_alias="ini")
    swinc: _Literal[1, 0] | None = _Field(default=None, validation_alias="inc")
    swcrp: _Literal[1, 0] | None = _Field(default=None, validation_alias="crp")
    swstr: _Literal[1, 0] | None = _Field(default=None, validation_alias="str")
    swirg: _Literal[1, 0] | None = _Field(default=None, validation_alias="irg")
    swcsv: _Literal[1, 0] | None = _Field(default=None, validation_alias="csv")
    swcsv_tz: _Literal[1, 0] | None = _Field(default=None, validation_alias="csv_tz")


class GeneralSettings(_PySWAPBaseModel, _SerializableMixin, _YAMLValidatorMixin):
    """模拟的通用设置。

    属性：
        pathwork (str): 工作目录路径。不可变属性。
        pathatm (str): 气象文件文件夹路径。不可变属性。
        pathcrop (str): 作物文件文件夹路径。不可变属性。
        pathdrain (str): 排水文件文件夹路径。不可变属性。
        swscre (Literal[0, 1, 3]): 开关，在屏幕上显示模拟运行进度
        swerror (Literal[0, 1]): 打印错误到屏幕的开关
        tstart (d): 模拟运行开始日期。
        tend (d): 模拟运行结束日期。
        nprintday (int): 每天的输出次数
        swmonth (Literal[0, 1]): 开关，每月输出
        swyrvar (Literal[0, 1]): *.BAL 和 *.BLC 文件中总水和溶质平衡的输出时间：
            选择每年固定日期或不同日期输出
        period (Optional[int]): 固定输出间隔
        swres (Optional[Literal[0, 1]]): 开关，每年重置输出间隔计数器
        swodat (Optional[Literal[0, 1]]): 开关，下方表格中给出额外输出日期
        outdatin (Optional[DateList]): 特定日期列表
        datefix (Optional[DayMonth]): 固定输出日期
        outdat (Optional[DateList]): 指定所有输出日期
        outfil (str): 输出文件的通用文件名。不可变属性。
        swheader (Literal[0, 1]): 在每个平衡期开始时打印标题
        extensions (list): SWAP 应返回的文件扩展名列表。
            可用选项包括：["wba", "end", "vap", "bal", "blc", "sba", "ate",
            "bma", "drf", "swb", "ini", "inc", "crp", "str", "irg", "csv", "csv_tz"]
        inlist_csv (Optional[StringList]): CSV 输出的变量列表
        inlist_csv_tz (Optional[StringList]): CSV 时区输出的变量列表
        swafo (Literal[0, 1, 2]): 开关，输出格式化水文数据文件
        swaun (Literal[0, 1, 2]): 开关，输出未格式化水文数据文件
        critdevmasbal (Optional[float]): 期间水平衡中的临界偏差
        swdiscrvert (Literal[0, 1]): 垂直离散化转换开关
        numnodnew (Optional[int]): 新节点数
        dznew (Optional[FloatList]): 隔室厚度
    """

    model_config = _ConfigDict(
        validate_assignment=True, use_enum_values=True, extra="ignore"
    )
    _all_extensions: _ClassVar[list[str]] = _EXTENSIONS
    extensions: list[str] = _Field(default_factory=list, exclude=True)
    exts: _Subsection[_ExtensionMixin] | None = None

    pathwork: _String = _Field(default=_BASE_PATH, frozen=True)
    pathatm: _String = _Field(default=_BASE_PATH, frozen=True)
    pathcrop: _String = _Field(default=_BASE_PATH, frozen=True)
    pathdrain: _String = _Field(default=_BASE_PATH, frozen=True)
    swscre: _Literal[0, 1, 3] = 0
    swerror: _Literal[0, 1] = 0

    tstart: _date | None = None
    tend: _date | None = None

    nprintday: int = _Field(default=1, ge=1, le=1440)
    swmonth: _Literal[0, 1] | None = None  # 1
    swyrvar: _Literal[0, 1] | None = None  # 0
    period: int | None = _Field(default=None, **_YEARRANGE)
    swres: _Literal[0, 1] | None = None
    swodat: _Literal[0, 1] | None = None
    outdatin: _Arrays | None = None
    datefix: _DayMonth | None = None
    outdat: _Arrays | None = None

    outfil: _String = _Field(default=_FNAME_OUT, frozen=True)
    swheader: _Literal[0, 1] = 0

    inlist_csv: _StringList | None = None
    inlist_csv_tz: _StringList | None = None
    swafo: _Literal[0, 1, 2] = 0
    swaun: _Literal[0, 1, 2] = 0
    critdevmasbal: float | None = _Field(default=None, **_UNITRANGE)
    swdiscrvert: _Literal[0, 1] = 0
    numnodnew: int | None = None
    dznew: _Arrays | None = None

    @_model_validator(mode="after")
    def validate_extensions(self):
        invalid_extensions = [
            ext for ext in self.extensions if ext not in self._all_extensions
        ]
        if invalid_extensions:
            msg = f"Invalid extensions: {', '.join(invalid_extensions)}"
            raise ValueError(msg)

        # 创建 _ExtensionMixin 对象而不触发验证
        object.__setattr__(
            self,
            "exts",
            _ExtensionMixin(**{
                ext: 1 if ext in self.extensions else 0 for ext in self._all_extensions
            }),
        )
        return self


class RichardsSettings(_PySWAPBaseModel, _SerializableMixin, _YAMLValidatorMixin):
    """Richards 方程的设置。

    属性：
        swkmean (Literal[1, 2, 3, 4, 5, 6]): 水力传导率平均方法开关
        swkimpl (Literal[0, 1]): 迭代期间更新水力传导率的开关
        dtmin (float): 最小时间步长 [1.d-7..0.1 d]
        dtmax (float): 最大时间步长 [dtmin..1 d]
        gwlconv (float): 时间步长之间地下水位的最大差异 [1.d-5..1000 cm]
        critdevh1cp (float): 每个隔室压头的最大相对差异 [1.0d-10..1.d3]
        critdevh2cp (float): 每个隔室压头的最大绝对差异 [1.0d-10..1.d3 cm]
        critdevponddt (float): 积水层最大水平衡误差 [1.0d-6..0.1 cm]
        maxit (int): 最大迭代次数 [5..100]
        maxbacktr (int): 迭代周期内最大回溯次数 [1..10]
    """

    swkmean: _Literal[1, 2, 3, 4, 5, 6] | None = None
    swkimpl: _Literal[0, 1] | None = None
    dtmin: float | None = _Field(default=0.000001, ge=1e-7, le=0.1)
    dtmax: float | None = _Field(default=0.04, ge=0.000001, le=1.0)
    gwlconv: float | None = _Field(default=100.0, ge=1e-5, le=1000.0)
    critdevh1cp: float | None = _Field(default=0.01, ge=1e-10, le=1e3)
    critdevh2cp: float | None = _Field(default=0.1, ge=1e-10, le=1e3)
    critdevponddt: float | None = _Field(default=0.0001, ge=1e-6, le=0.1)
    maxit: int | None = _Field(default=30, ge=5, le=100)
    maxbacktr: int | None = _Field(default=3, ge=1, le=10)
