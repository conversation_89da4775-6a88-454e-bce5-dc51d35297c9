# 导入

在导入方面，需要在保持简洁和清晰组织之间取得平衡。一方面，输入 `package.` 并访问该包中的任何内容很方便。然而，随着项目的增长，查找所需内容可能会变得令人不知所措。在 pySWAP 中，我们选择限制导入快捷方式，使模块调用更加明确。

!!! note

    为了确保清晰度和可维护性，我们精心组织了导入。

## 1. 组件

组件库中的单个类不会直接在包级别公开。要访问它们，请使用以下方法：

```python
import pyswap as psp

simset = psp.components.simsettings.GeneralSettings(...)
```

虽然某些模块可能只包含一两个类，但其他模块包含许多表，这会使提示变得混乱。

## 2. 特殊模块

`model`、`db`、`io`、`gis` 和 `plot` 等特殊模块可以直接从包级别访问：

```python
import pyswap as psp

location = psp.gis.Location(...)

model = psp.Model(...)

psp.plot_evapotranspiration(...)
```

## 3. 测试用例

测试用例可以像这样导入以进行训练和实验：

```python
from pyswap import testcase
```

# 第三方和非重新导出导入

为了使第三方导入和其他不打算重新导出的导入保持组织，贡献者应在用户通常调用的模块（例如 `pyswap.components`）中使用带下划线前缀的别名。这确保了重要的导入出现在 IDE 提示的顶部。这是一个示例：

```python
import numpy as _np
import pandas as _pd
import matplotlib.pyplot as _plt
# 内部定义的不打算重新导出的对象也应
# 使用下划线作为别名
from pyswap.core.basemodel import PySWAPBaseModel as _PySWAPBaseModel
```

在重要类可以直接从包级别访问的模块中，这不是必需的。

!!! note

    请注意，我们也更喜欢显式（完整）导入路径而不是相对导入，因此：

    ``` Python
    from .core import db  # 不好
    from pyswap.core import db  # 好
    ```

# 导入风格

Ruff linting 工具设置为强制单行导入，并包装别名，因此首选样式如下：

```Python
from pyswap.core.valueranges import UNITRANGE as _UNITRANGE
from pyswap.core.fields import (
    Decimal2f as _Decimal2f,
    Decimal3f as _Decimal3f,
    String as _String,
    Table as _Table,
)
```

# 使用 **all**

为了一致性，在每个模块的顶部，应该有一个 `__all__` 变量，定义哪些对象可以通过 `*` 通配符导入。
