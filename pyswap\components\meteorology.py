# mypy: disable-error-code="call-overload, misc, operator"
# operator 错误是由于 Pydantic 验证器引起的。当前的实现是正确的，因此这不是优先修复的问题。


"""气象设置和数据。

此模块包含用于处理模拟气象设置和数据的类和函数。

类：
    MetFile: .met 文件的气象数据。
    Meteorology: 模拟的气象设置。

函数：
    load_from_csv: 从 CSV 文件加载气象数据。
    load_from_knmi: 从 KNMI API 加载气象数据。
"""

from datetime import datetime as _datetime
from typing import (
    Literal as _Literal,
)

from knmi import (
    get_day_data_dataframe as _get_day_data_dataframe,
    get_hour_data_dataframe as _get_hour_data_dataframe,
)
from pandas import read_csv as _read_csv
from pydantic import (
    Field as _Field,
    PrivateAttr as _PrivateAttr,
)

from pyswap.core.basemodel import PySWAPBaseModel as _PySWAPBaseModel
from pyswap.core.fields import (
    CSVTable as _CSVTable,
    Decimal2f as _Decimal2f,
    File as _File,
    String as _String,
    Table as _Table,
)
from pyswap.core.valueranges import UNITRANGE as _UNITRANGE
from pyswap.gis import Location as _Location
from pyswap.utils.mixins import (
    FileMixin as _FileMixin,
    SerializableMixin as _SerializableMixin,
    YAMLValidatorMixin as _YAMLValidatorMixin,
)

__all__ = [
    "MetFile", 
    "Meteorology", 
    "metfile_from_csv", 
    "metfile_from_knmi"
]


class MetFile(_PySWAPBaseModel, _FileMixin, _SerializableMixin):
    """ .met 文件的气象数据。

    此对象由从各种来源获取或加载气象数据的函数创建。
    数据存储为 pandas.DataFrame，但使用 CSVTable 字段类型的自定义字段序列化器进行格式化。

    属性：
        metfil (str): .met 文件名
        content (CSVTable): 气象数据文件
    """

    # None，因为必须将扩展名添加到 metfil
    _extension: bool = _PrivateAttr(default=None)

    metfil: _String
    content: _CSVTable | None = _Field(default=None, exclude=True)


class Meteorology(_PySWAPBaseModel, _SerializableMixin, _YAMLValidatorMixin):
    """模拟的气象设置。

    !!! 注意
        SWRAIN 和 SWETSINE 应该是可选的，
        但 Fortran 代码无论如何都会评估它们的存在。它们默认设置为 0。


    属性：
        meteo_location (Location): 一个点 GIS 对象。如果提供，则不得提供 lat 和 alt。
            默认情况下它们会被覆盖。
        lat (Decimal): 气象站的纬度 [度]。
        swetr (int): 潜在蒸散量的气象数据类型开关：

            * 0 - 使用基本气象数据并应用 Penman-Monteith 方程。
            * 1 - 结合作物系数使用参考蒸散量数据。

        swdivide (int): E 和 T 分布的开关。默认为 0：

            * 0 - 基于作物和土壤因子。
            * 1 - 基于 Penman-Monteith 的直接应用。

        swmetdetail (int): 蒸散量和降雨气象数据时间间隔的开关：

            * 0 - 每日数据。
            * 1 - 次日数据。

        swrain (int): 实际降雨强度使用开关，默认为 0：

            * 0 - 使用每日降雨量。
            * 1 - 使用每日降雨量 + 平均强度。
            * 2 - 使用每日降雨量 + 持续时间。
            * 3 - 使用详细降雨记录（dt < 1 天），如单独文件中提供。

        swetsine (int): 开关，根据正弦波分布每日 Tp 和 Ep，默认为 0：

            * 0 - 不分布。
            * 1 - 根据正弦波分布 Tp 和 Ep。

        metfile (MetFile): 包含要保存到 .met 文件的气象数据的 MetFile 模型。
        alt (Decimal): 气象站的海拔 [m]。
        altw (Decimal): 风的高度 [m]。
        angstroma (Decimal): 阴天到达地球的地球外辐射分数。
        angstromb (Decimal): 晴天到达地球的地球外辐射的额外分数。
        table_rainflux (Table): 降雨强度 RAINFLUX 随时间 TIME 变化的表格。
        rainfil (str): 详细降雨数据的文件名。
        nmetdetail (int): 每天气象数据记录的数量。

    属性：
        met: 返回 met 文件的字符串表示。

    方法：
        write_met: 写入 .met 文件。
    """

    lat: _Decimal2f | None = _Field(default=None, ge=-90, le=90)
    meteo_location: _Location | None = _Field(default=None, exclude=True)
    swetr: _Literal[0, 1] | None = None
    swdivide: _Literal[0, 1] | None = None
    swrain: _Literal[0, 1, 2, 3] | None = 0
    swetsine: _Literal[0, 1] = 0
    metfile: _File | None = _Field(default=None, repr=False)
    alt: _Decimal2f | None = _Field(default=None, ge=-400.0, le=3000.0)
    altw: _Decimal2f = _Field(default=None, ge=0.0, le=99.0)
    angstroma: _Decimal2f = _Field(default=None, **_UNITRANGE)
    angstromb: _Decimal2f = _Field(default=None, **_UNITRANGE)
    swmetdetail: _Literal[0, 1] | None = None
    table_rainflux: _Table | None = None
    rainfil: _String | None = None
    nmetdetail: int | None = _Field(default=None, ge=1, le=96)

    @property
    def met(self):
        return self.metfile.content.to_csv(index=False, lineterminator="\n")

    def model_post_init(self, __context=None):
        """如果提供了 Location 对象，则从 `meteo_location` 设置 lat 和 alt。"""
        if self.meteo_location:
            self.lat = self.meteo_location.lat
            self.alt = self.meteo_location.alt

        self._validation = True
        self.validate_with_yaml()
        self._validation = False

    def write_met(self, path: str):
        """写入 .met 文件。

        !!! 注意

            在此函数中，未传递扩展名，因为
            swp 文件要求 metfile 参数已随扩展名一起传递。

        参数：
            path (str): 文件路径。
        """

        self.metfile.save_file(string=self.met, fname=self.metfile.metfil, path=path)


def metfile_from_csv(metfil: str, csv_path: str, **kwargs) -> MetFile:
    """从 CSV 文件加载气象数据的方法。

    参数：
        metfil (str): .met 文件名
        csv_path (str): CSV 文件路径
        **kwargs (dict): pandas.read_csv 的关键字参数

    返回：
        MetFile 对象。
    """

    return MetFile(metfil=metfil, content=_read_csv(csv_path, **kwargs))


def metfile_from_knmi(
    metfil: str,
    stations: str | list,
    variables: list[
        _Literal[
            "WIND",
            "TEMP",
            "SUNR",
            "PRCP",
            "VICL",
            "WEER",
            "DD",
            "FH",
            "FF",
            "FX",
            "T",
            "T10N",
            "TD",
            "SQ",
            "Q",
            "DR",
            "RH",
            "P",
            "VV",
            "N",
            "U",
            "WW",
            "IX",
            "M",
            "R",
            "S",
            "O",
            "Y",
            "UG",
            "FG",
            "UX",
            "UN",
        ]
    ],
    start: str | _datetime = "20000101",
    end: str | _datetime = "20200101",
    frequency: _Literal["day", "hour"] = "day",
    inseason: bool = False,
) -> MetFile:
    """使用 knmi-py 从 KNMI API 检索气象数据并强制执行 SWAP 所需的格式。

    参数：
        metfil (str): .met 文件名
        stations (str | list): 要从中检索数据的站点编号
        variables (str | list): 要检索的变量
        start (str | dt): 数据的开始日期
        end (str | dt): 数据的结束日期
        frequency (Literal['day', 'hour']): 数据的频率（日或小时）
        inseason (bool): 是否检索季节内数据

    返回：
        MetFile 对象。
    """

    if isinstance(stations, str):
        stations = [stations]
    if isinstance(variables, str):
        variables = [variables]

    if not variables:
        variables = ["TEMP", "PRCP", "Q", "UG", "FG", "UX", "UN"]

    get_func = (
        _get_day_data_dataframe if frequency == "day" else _get_hour_data_dataframe
    )

    df = get_func(
        stations=stations, start=start, end=end, variables=variables, inseason=inseason
    )

    # 重命名一些列
    required_column_names = {
        "STN": "Station",
        "TN": "Tmin",
        "TX": "Tmax",
        "UG": "HUM",
        "DR": "WET",
        "FG": "WIND",
        "RH": "RAIN",
        "EV24": "ETref",
        "Q": "RAD",
    }

    df = df.rename(columns=required_column_names)

    # 参数重新计算，原始单位是 0.1 单位
    df[["Tmin", "Tmax", "ETref", "RAIN", "WIND"]] = df[
        ["Tmin", "Tmax", "ETref", "RAIN", "WIND"]
    ].multiply(0.1)

    # 所需单位是天
    df["WET"] = df["WET"].multiply(0.1).multiply(24)

    return MetFile(metfil=metfil, content=df)


meteo_tables = ["SHORTINTERVALMETEODATA", "DETAILEDRAINFALL", "RAINFLUX"]
__all__.extend(meteo_tables)
