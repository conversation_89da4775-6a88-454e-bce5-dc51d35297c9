{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 基本用法\n", "\n", "欢迎来到 pySWAP 包的基本用法笔记本。在这里，我们将介绍如何在 pySWAP 中构建 SWAP 模型的基础知识，加载一个测试用例模型并进行一些探索。"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pyswap as psp\n", "\n", "psp.log.set_log_level(\"WARNING\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["pySWAP 是一个面向对象的库，这意味着我们尝试将 SWAP 模型设置中不同的部分封装成对象。这些对象需要在初始阶段由用户定义，然后可以方便地进行操作、复制和比较。以下是气象设置对象的示例："]}, {"cell_type": "markdown", "metadata": {}, "source": ["## <PERSON><PERSON><PERSON><PERSON> 测试用例\n", "\n", "许多模型设置仍然需要明确写出。不过，我们正在努力将初始模型必须编写的代码量减少到最低。如果您对 Hupselbrook 模型在 pySWAP 中是如何实现的感兴趣，请查看 `testcase` 模块中的 `hupselbrook.py` 文件。\n", "\n", "让我们加载模型并检查元数据。"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ml = psp.testcase.get(\"hups<PERSON><PERSON>\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["`Model` 对象有一个 `run` 方法，可以运行模型并将结果捕获到一个 `Result` 对象中。唯一需要传递的参数是创建临时目录的路径。"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(ml.swp)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["result = ml.run()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["从现在开始，我们可以使用 Result 对象的属性和计算字段来访问结果。例如，.csv 文件已经被加载为 DataFrame。"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["result.output.keys()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["result.yearly_summary"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(result.blc_summary)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["result.csv[[\"RAIN\", \"IRRIG\", \"INTERC\"]].plot()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 加载模型的替代方法\n", "\n", "从 pyswap 0.2.0 开始，可以从经典的 ascii 配置文件中加载整个模型。为此，您需要单独创建 swp 和其他相关文件，然后将它们组合成一个模型。您可以在[加载经典 SWAP 模型](/tutorials/loading-classic-swap)的教程中看到具体操作。"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 更改模型设置\n", "\n", "假设您想要添加另一个要生成的文件扩展名，或者进行任何其他更改。最好的方法是为您想要更改的组件创建一个副本，然后用这个新组件更新模型，并保存其副本。"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 更新常规设置，包括 inlist_csv_tz\n", "ml.generalsettings.extensions = [*ml.generalsettings.extensions, \"csv_tz\"]\n", "ml.generalsettings.inlist_csv_tz = [\"WC\"]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["result2 = ml.run()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from pyswap.core.plot import water_content\n", "\n", "wc_df = result2.csv_tz.reset_index()\n", "\n", "water_content(wc_df, \"DEPTH\", \"DATE\", \"WC\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 设置模型的替代方法\n", "\n", "最初，只能将生成的模型组件直接分配给 Model 对象的属性。从最新版本（2025年2月）开始，可以通过加载传统上用于设置模型的纯文本文件来设置和运行模型。请参阅[此笔记本](/tutorials/004-loading-classic-swap/)了解如何操作！"]}], "metadata": {"kernelspec": {"display_name": "pyswap-U62T3krn-py3.11", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 2}