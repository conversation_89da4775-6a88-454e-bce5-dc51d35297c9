.PHONY: test-hupsel
test-hupsel: ## 安装 poetry 环境并安装 pre-commit 钩子
	@echo "正在测试 Hupselbrook 示例"
	@poetry run pytest tests/test_cases.py::test_hupselbrook_model

.PHONY: test-grass
test-grass: ## 安装 poetry 环境并安装 pre-commit 钩子
	@echo "正在运行草地生长示例"
	@poetry run pytest tests/test_cases.py::test_grassgrowth

.PHONY: testcheck
testcheck: ## 在测试配置中运行代码质量工具
	@echo "🚀 检查 Poetry 锁定文件与 'pyproject.toml' 的一致性：正在运行 poetry check --lock"
	@poetry check --lock
	@echo "🚀 正在检查代码：正在运行 pre-commit"
	@poetry run pre-commit run -a 2>&1 | tee .logs/log_ruff_raw.md
	@echo "🚀 静态类型检查：正在运行 mypy"
	@poetry run mypy pyswap --config-file mypy.dev.ini 2>&1 | tee .logs/log_mypy_raw.md
	@echo "🚀 检查过时依赖项：正在运行 deptry"
	@poetry run deptry . 2>&1 | tee .logs/log_deptry_raw.md

.PHONY: check
check: ## 运行代码质量工具。
	@echo "🚀 检查 Poetry 锁定文件与 'pyproject.toml' 的一致性：正在运行 poetry check --lock"
	@poetry check --lock
	@echo "🚀 正在检查代码：正在运行 pre-commit"
	@poetry run pre-commit run -a
	@echo "🚀 静态类型检查：正在运行 mypy"
	@poetry run mypy
	@echo "🚀 检查过时依赖项：正在运行 deptry"
	@poetry run deptry .

.PHONY: test
test: ## 使用 pytest 测试代码
	@echo "🚀 正在测试代码：正在运行 pytest"
	@poetry run pytest --cov --cov-config=pyproject.toml --cov-report=xml

.PHONY: build
build: clean-build ## 使用 poetry 构建 wheel 文件
	@echo "🚀 正在创建 wheel 文件"
	@poetry build

.PHONY: clean-build
clean-build: ## 清理构建产物
	@rm -rf dist

.PHONY: publish
publish: ## 发布到 pypi。
	@echo "🚀 正在发布：试运行。"
	@poetry config pypi-token.pypi $(PYPI_TOKEN)
	@poetry publish --dry-run
	@echo "🚀 正在发布。"
	@poetry publish

.PHONY: build-and-publish
build-and-publish: build publish ## 构建并发布。

.PHONY: docs-test
docs-test: ## 测试文档是否可以在没有警告或错误的情况下构建
	@poetry run mkdocs build -s

.PHONY: docs
docs: ## 构建并提供文档
	@poetry run mkdocs serve

.PHONY: help
help:
	@grep -E '^[a-zA-Z_-]+:.*?## .*$$' $(MAKEFILE_LIST) | awk 'BEGIN {FS = ":.*?## "}; {printf "\033[36m%-20s\033[0m %s\n", $$1, $$2}'

.DEFAULT_GOAL := help
