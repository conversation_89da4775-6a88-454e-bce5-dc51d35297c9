"""与 ASCII 文件交互。

函数：
    open_ascii: 打开 ASCII 文件并检测其编码。
    save_ascii: 将字符串保存到 ASCII 文件。
"""

from pathlib import Path

import chardet


def open_ascii(file_path: Path) -> str:
    """打开文件并检测编码。

    参数：
        file_path (str): 要打开的文件路径。
    """
    with open(file_path, "rb") as f:
        raw_data = f.read()
    encoding = chardet.detect(raw_data)["encoding"]

    return raw_data.decode(encoding)


def save_ascii(
    string: str,
    fname: str,
    path: str,
    mode: str = "w",
    extension: str | None = None,
    encoding: str = "ascii",
) -> None:
    """
    将字符串保存到具有给定扩展名的文件。

    参数：
        string (str): 要保存到文件的字符串。
        extension (str): 文件应具有的扩展名（例如 'txt'、'csv' 等）。
        fname (str): 文件名。
        path (str): 文件应保存的路径。
        mode (str): 文件应打开的模式（例如 'w' 用于写入，'a' 用于追加等）。
        encoding (str): 用于文件的编码（默认为 'ascii'）。

    返回：
        None
    """

    if extension is not None:
        fname = f"{fname}.{extension}"

    with open(f"{path}/{fname}", f"{mode}", encoding=f"{encoding}") as f:
        f.write(string)
