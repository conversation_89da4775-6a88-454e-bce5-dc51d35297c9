{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Using HDF5 database\n", "\n", "This tutorial shows how to store and retrieve models and their results from an HDF5 database ([see more](/user-guide/hdf5/)) integrated in pySWAP. We are going to:\n", "- Load a test case model.\n", "- Create an HDF5 database and save a model inside it.\n", "- Retrieve a model from the HDF5 database.\n", "- Alter the model and save its version back to the database.\n", "- Compare the results.\n", "\n", "We will also see how to do run a list of models in parallel."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from pyswap import db, run_parallel, testcase\n", "\n", "# get the Model object and run it\n", "ml = testcase.get(\"h<PERSON><PERSON><PERSON>\")\n", "result = ml.run()\n", "result.csv.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# create an interface object and save the model and the result\n", "f = db.HDF5(filename=\"data.h5\")\n", "f.save_model(model=ml, result=result)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Checking saved model\n", "\n", "we can now check what is in the database. By calling `.list_models` property we will get a dictionary where keys are projects and values are lists of models inside the project."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["f.list_models"]}, {"cell_type": "markdown", "metadata": {}, "source": ["To retrieve a models from the database, use `.load()` method. It will "]}, {"cell_type": "markdown", "metadata": {"vscode": {"languageId": "markdown"}}, "source": ["## Loading models\n", "\n", "Above you can see that the model was saved to the .h5 file. If the file already exists (for example one with your other projects), you will get a warning message and the program skips creation of additional elements in the database.\n", "\n", "Now we are going to:\n", "- Load a specific model (or all models from a project) from the HDF5 file.\n", "- Create a copy of the loaded model and modify its parameters for another run."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["db = f.load(\"psp test - hupselbrook\", load_results=True)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["As you can see below, the result of `.load()` is a dictionary where the key is the model version name, and the value is a tuple of `Model` and `Result` objects (or `Model` and None in case `load_results=False`)."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["db"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Creating model variants\n", "\n", "we can create variants of the initial model by making copies of the initial model and updating one or more variables."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# For no reason, we will create 38 new versions, where in each version the drainage depth is increased by 2 cm\n", "version = {f\"drain_down_{i * 5}\": {\"zbotdr\": -80.0 - i * 5} for i in range(1, 10)}"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ml2 = db.get(\"base\")[0]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Now we will make it a bit more complicated on purpose, to additionally show how to update nested objects. Above we defined the dictionary where the key is the new model version name and the values are new values for given attributes. As we cannot do a direct update of attributes of nested objects, we need to do it step by step:\n", "- First we make new updated copies of the DraFile object from the initial model\n", "- Then we make copies of Drainage objects from the initial model\n", "- Last we create 3 copies of the model with updated lateraldrainage attribute.\n", "\n", "We can use dictionary comprehension to get a dictionary of updated `DraFile` objects, which is nested in the lateraldrainage. The `.update()` method returns updated copies."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["versions = {\n", "    key: ml2.lateraldrainage.drafile.update(value) for key, value in version.items()\n", "}"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Now we create new models with the lateraldrainage attribute updated with the new `DraFile` objects. The list comprehension below returns a list of new `Model` objects updated with copies of `Drainage` objects:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["mls = [\n", "    ml2.update({\n", "        \"version\": key,\n", "        \"lateraldrainage\": ml2.lateraldrainage.update({\"drafile\": value}),\n", "    })\n", "    for key, value in versions.items()\n", "]\n", "print(\"There are\", len(mls), \"model versions\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Run in parallel and save in .h5\n", "\n", "pyswap now has a simple way to parallellize running of the models. Instead of calling the .run() method, you need to use run_parallel() function, providing it with a list of model objects. A list of Result objects is returned."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["results = run_parallel(mls, silence_warnings=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["_ = [\n", "    f.save_model(model=ml, result=result)\n", "    for ml, result in zip(mls, results, strict=False)\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["f.list_models"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Visualise the results\n", "\n", "Now you can observe the results by plotting the main output pandas.DataFrame object, like you normally would."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["result.csv[\"GWL\"].plot()\n", "_ = [result.csv[\"GWL\"].plot() for result in results]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Deleting objects\n", "\n", "You can use the `.delete()` method to delete the objects from the hdf5 database. You can delete either a specific model or the entire project. Use this method only for small deletions, as for now it does not perform repacking of the HDF5 file (the objects are deleted but the disk space is not freed). For large deletions, consider creating a new HDF5 file and saving only the models you want to keep."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["f.list_models"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["f.delete(project=\"psp test - hupselbrook\", model=\"base\")\n", "f.list_models"]}, {"cell_type": "markdown", "metadata": {}, "source": ["You can also delete the entire project. This will remove the objects from the database, but the .h5 file will still have the same size. Therefore, for large deletions, it's better to replace the file entirely."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["f.delete(project=\"psp test - hupselbrook\")\n", "f.list_models"]}], "metadata": {"kernelspec": {"display_name": "pyswap-U62T3krn-py3.11", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 2}