#!/usr/bin/env python3
"""
运行 Hupselbrook 测试用例并显示输出文件位置
"""

import pyswap as psp
from pathlib import Path

# 设置日志级别
psp.set_log_level("INFO")

# 直接创建一个简化的模型，避免使用缺失的 WOFOST 数据库
print("正在创建简化的 Hupselbrook 模型...")
model = psp.Model()

# 基本元数据
meta = psp.components.Metadata(
    author="Test User",
    institution="Test Institution",
    email="<EMAIL>",
    project="hupselbrook test",
    swap_ver="4.2",
)
model.metadata = meta

# 基本设置
simset = psp.components.simsettings.GeneralSettings(
    tstart="2002-01-01",
    tend="2002-12-31",  # 缩短模拟时间
    extensions=["csv"],  # 只输出 CSV
    nprintday=1,
    swerror=1,
    swscre=0,
    swmonth=1,
    swyrvar=0,
    datefix="31 12",
    inlist_csv=["rain", "irrig", "interc", "runoff", "drainage", "epot", "eact"],
)
model.generalsettings = simset

# 气象设置
meteo_location = psp.gis.Location(lat=52.0, lon=21.0, alt=10.0)
meteo_data = psp.components.meteorology.MetFile(
    metfil="283.met", content=psp.testcase.load_met("hupselbrook")
)
meteo = psp.components.meteorology.Meteorology(
    meteo_location=meteo_location,
    swetr=0,
    metfile=meteo_data,
    swdivide=1,
    swmetdetail=0,
    altw=10.0,
    angstroma=0.25,
    angstromb=0.5,
)
model.meteorology = meteo

# 简化的作物设置（只使用固定作物）
crop = psp.components.crop.Crop(swcrop=0)  # 裸土
model.crop = crop

# 土壤水分设置
soilmoisture = psp.components.soilwater.SoilMoisture(swinco=2, gwli=-75.0)
model.soilmoisture = soilmoisture

# 表面流设置
surfaceflow = psp.components.soilwater.SurfaceFlow(
    swpondmx=0, pondmx=0.2, rsro=0.5, rsroexp=1.0, swrunon=0
)
model.surfaceflow = surfaceflow

# 蒸发设置
evaporation = psp.components.soilwater.Evaporation(
    cfevappond=1.25, swcfbs=0, rsoil=30.0, swredu=1, cofredbl=0.35, rsigni=0.5
)
model.evaporation = evaporation

# 土壤剖面
soil_profile = psp.components.soilwater.SOILPROFILE.create({
    "ISUBLAY": [1, 2],
    "ISOILLAY": [1, 1],
    "HSUBLAY": [50.0, 150.0],
    "HCOMP": [10.0, 10.0],
    "NCOMP": [5, 15],
})

soil_hydraulic_functions = psp.components.soilwater.SOILHYDRFUNC.create({
    "ORES": [0.01],
    "OSAT": [0.42],
    "ALFA": [0.0276],
    "NPAR": [1.491],
    "KSATFIT": [12.52],
    "LEXP": [-1.060],
    "ALFAW": [0.0542],
    "H_ENPR": [0.0],
    "KSATEXM": [12.52],
    "BDENS": [1315.0],
})

soilprofile = psp.components.soilwater.SoilProfile(
    swsophy=0,
    soilprofile=soil_profile,
    swhyst=0,
    tau=0.2,
    soilhydrfunc=soil_hydraulic_functions,
    swmacro=0,
)
model.soilprofile = soilprofile

# 排水设置
drainage = psp.components.drainage.Drainage(swdra=0)  # 无排水
model.lateraldrainage = drainage

# 底部边界
bottom_boundary = psp.components.boundary.BottomBoundary(swbbcfile=0, swbotb=6)
model.bottomboundary = bottom_boundary

# 创建输出目录
output_dir = Path("./hupselbrook_output")
output_dir.mkdir(exist_ok=True)

print(f"输出目录: {output_dir.absolute()}")

# 运行模型
print("正在运行模型...")
result = model.run(path=output_dir)

print("模型运行完成!")
print(f"输出文件保存在: {output_dir.absolute()}")

# 显示可用的输出
print("\n可用的输出:")
print(f"- 输出键: {list(result.output.keys())}")

# 显示年度汇总
if result.csv is not None:
    print("\n年度汇总:")
    print(result.yearly_summary)
    
    # 保存 CSV 输出到文件
    csv_output_path = output_dir / "hupselbrook_results.csv"
    result.csv.to_csv(csv_output_path)
    print(f"\nCSV 结果已保存到: {csv_output_path.absolute()}")

# 显示日志信息
if result.log:
    log_path = output_dir / "model_log.txt"
    with open(log_path, 'w', encoding='utf-8') as f:
        f.write(result.log)
    print(f"模型日志已保存到: {log_path.absolute()}")

print("\n运行完成!")
