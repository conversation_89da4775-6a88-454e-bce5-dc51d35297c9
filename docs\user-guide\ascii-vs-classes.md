# SWAP 模型设置

要运行 SWAP 模型，您需要提供在多个 ASCII 文件中定义的各种参数。主文件（扩展名为 `.swp`）必须始终与 SWAP 可执行文件一起存在。根据此文件中的设置，可能需要其他文件，例如用于作物生长模型设置的 `.crp` 文件。每个文件都包含键值设置或表格数据，通常按代表不同模型组件的部分进行分组。在 `pyswap` 中，这些变量组表示为类。

=== "Classic SWAP"

    ```txt
    * Part 8: Snow and frost

    * Switch, calculate snow accumulation and melt:
      SWSNOW = 1                 ! 0 = no simulation of snow
                                ! 1 = simulation of snow accumulation and melt

    * If SWSNOW = 1, specify:
      SNOWINCO = 22.0            ! Initial snow water equivalent [0..1000 cm, R]
      TEPRRAIN = 2.0             ! Temperature above which all precipitation is rain[ 0..10 degree C, R]
      TEPRSNOW = -2.0            ! Temperature below which all precipitation is snow[-10..0 degree C, R]
      SNOWCOEF = 0.3             ! Snowmelt calibration factor [0...10 -, R]

    * Switch, in case of frost reduce soil water flow:
      SWFROST = 1                ! 0 = no simulation of frost
                                ! 1 = simulation of reduction soil water flow due to frost

    * If SWFROST = 1, then specify soil temperature range in which soil water flow is reduced
      TFROSTSTA = 0.0            ! Soil temperature where reduction of water fluxes starts [-10.0,5.0, degree C, R]
      TFROSTEND = -1.0           ! Soil temperature where reduction of water fluxes ends [-10.0,5.0, degree C, R]

    **********************************************************************************
    ```

=== "pyswap class"

    ``` py
    import pyswap as psp

    snow = psp.components.soilwater.SnowAndFrost(  # (1)!
      swsnow=1, snowinco=22.0, teprrain=2.0, teprsnow=-2.0,
      snowcoef=0.3, swfrost=1, tfroststa=0.0, tfrostend=-1.0,
    )
    ```

    1.  当您使用像 VS Code 或 PyCharm 这样的代码编辑器时，您开始键入模块名称，您将获得类型提示和模块文档，这将帮助您浏览包。

类通常充当变量的“容器”，但有些也包含其部分特有的功能，例如从 CSV 文件读取和格式化气象数据。类定义提供验证、类型提示和内置文档，大多数代码编辑器都可以利用这些功能。如果您刚开始使用该模型，这会特别有用，因为它使理解和使用参数变得更容易。类将在下一节中更详细地解释。如果您正在寻找有关输入文件的更多信息，请访问 [wiki 部分](/wiki/)。
