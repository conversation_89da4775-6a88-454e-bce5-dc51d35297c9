**********************************************************************************
* Filename: swap.dra
* Contents: SWAP 4 - Input data for basic and extended drainage
**********************************************************************************
* Comment area:
* Testbank of SWAP: build with template of dra-file
*
**********************************************************************************

*** BASIC DRAINAGE SECTION ***

**********************************************************************************
* Part 0: General

* Switch, method of lateral drainage calculation:
  DRAMET = {{DRAMET}}        ! 1 = Use table of drainage flux - groundwater level relation
                             ! 2 = Use drainage formula of Hooghoudt or Ernst
                             ! 3 = Use drainage/infiltration resistance, multi-level if needed

  SWDIVD = {{SWDIVD}}        ! Calculate vertical distribution of drainage flux in groundwater [Y=1, N=0]

{{#SWITCH_SWDIVD_OPTION_1}}
* If SWDIVD = 1, specify anisotropy factor COFANI (horizontal/vertical saturated hydraulic
* conductivity) for each soil layer (maximum MAHO), [0.0001..1000 -, R]:
  COFANI = {{COFANI}}
{{/SWITCH_SWDIVD_OPTION_1}}

* Switch to adjust upper boundary of model discharge layer
  SWDISLAY = 0               ! switch to adjust discharge layer  [0,1,2, -, I]

**********************************************************************************


{{#SWITCH_DRAMET_OPTION_1}}
**********************************************************************************
* Part 1: Table of drainage flux - groundwater level relation (DRAMET = 1)

{{#SWITCH_SWDIVD_OPTION_1}}
* If SWDIVD = 1, specify the drain spacing:
  LM1 = {{LM1}}              ! Drain spacing, [1..1000 m, R]

{{/SWITCH_SWDIVD_OPTION_1}}
* Specify drainage flux QDRAIN [-100..1000 cm/d, R] as function of groundwater level GWL [-1000.0..10.0 cm, R]
* negative GWL is below soil surface; start with highest groundwater level; maximum of 25 records:

{{#TABLE_QDRNTB}}
  {{QDRAIN}} {{GWL}}
{{/TABLE_QDRNTB}}
* End of table
{{/SWITCH_DRAMET_OPTION_1}}
{{#SWITCH_DRAMET_OPTION_2}}
**********************************************************************************
* Part 2: Drainage formula of Hooghoudt or Ernst (DRAMET = 2)
*
* Drain characteristics:
  LM2    = {{LM2}}           ! Drain spacing, [1..1000 m, R]
  SHAPE  = {{SHAPE_DRA}}     ! Shape factor to account for actual location between drain and water divide [0.0..1.0 -, R]
  WETPER = {{WETPER}}        ! Wet perimeter of the drain, [0..1000 cm, R]
  ZBOTDR = {{ZBOTDR}}        ! Level of drain bottom, [-1000..0 cm, R, neg. below soil surface]
  ENTRES = {{ENTRES}}        ! Drain entry resistance, [0..1000 d, R]

* Soil profile characteristics:

* Position of drain:
  IPOS   = {{IPOS}}          ! 1 = On top of an impervious layer in a homogeneous profile
                             ! 2 = Above an impervious layer in a homogeneous profile
                             ! 3 = At the interface of a fine upper and a coarse lower soil layer
                             ! 4 = In the lower, more coarse soil layer
                             ! 5 = In the upper, more fine soil layer

* For all positions specify:
  BASEGW = {{BASEGW}}        ! Level of impervious layer, [-1d4..0 cm, R]
  KHTOP  = {{KHTOP}}         ! Horizontal hydraulic conductivity top layer, [0..1000 cm/d, R]

{{#SWITCH_IPOS_OPTION_3}}
* In addition, in case IPOS = 3
  KHBOT  = {{KHBOT}}         ! horizontal hydraulic conductivity bottom layer, [0..1000 cm/d, R]
  ZINTF  = {{ZINTF}}         ! Level of interface of fine and coarse soil layer, [-1d4..0 cm, R]
{{/SWITCH_IPOS_OPTION_3}}
{{#SWITCH_IPOS_OPTION_4}}
* In addition, in case IPOS = 4
  KHBOT  = {{KHBOT}}         ! horizontal hydraulic conductivity bottom layer, [0..1000 cm/d, R]
  ZINTF  = {{ZINTF}}         ! Level of interface of fine and coarse soil layer, [-1d4..0 cm, R]
  KVTOP  = {{KVTOP}}         ! Vertical hydraulic conductivity top layer, [0..1000 cm/d, R]
  KVBOT  = {{KVBOT}}         ! Vertical hydraulic conductivity bottom layer, [0..1000 cm/d, R]
{{/SWITCH_IPOS_OPTION_4}}
{{#SWITCH_IPOS_OPTION_5}}
* In addition, in case IPOS = 5
  KHBOT  = {{KHBOT}}         ! horizontal hydraulic conductivity bottom layer, [0..1000 cm/d, R]
  ZINTF  = {{ZINTF}}         ! Level of interface of fine and coarse soil layer, [-1d4..0 cm, R]
  KVTOP  = {{KVTOP}}         ! Vertical hydraulic conductivity top layer, [0..1000 cm/d, R]
  KVBOT  = {{KVBOT}}         ! Vertical hydraulic conductivity bottom layer, [0..1000 cm/d, R]
  GEOFAC = {{GEOFAC}}        ! Geometry factor of Ernst,  [0..100 -, R]
{{/SWITCH_IPOS_OPTION_5}}
{{/SWITCH_DRAMET_OPTION_2}}
{{#SWITCH_DRAMET_OPTION_3}}
**********************************************************************************
* METHOD 3 - Part 3: Drainage and infiltration resistance (DRAMET = 3)

  NRLEVS = {{NRLEVS}}        ! Number of drainage levels, [1..5, I]

* Option for interflow in highest drainage level (shallow system with short residence time)
  SWINTFL = {{SWINTFL}}      ! Switch for interflow [0,1, I]

{{#SWITCH_SWINTFL_OPTION_1}}
* If SWINTFL = 1, specify:
  COFINTFLB = {{COFINTFLB}}  ! Coefficient for interflow relation  [0.01..10.0 d, R]
  EXPINTFLB = {{EXPINTFLB}}  ! Exponent for interflow relation  [0.1..1.0 -, R]

{{#SWITCH_SWDIVD_OPTION_1}}
* Switch to adjust the bottom of the model discharge layer in case of lateral interflow (SWDIVD=1).
* In case of SWTOPNRSRF=1) then the bottom of the highest order drainage system (ZBORDR(NUMDRAIN)) represents the max depth of the interflow.
 SWTOPNRSRF = {{SWTOPNRSRF}} ! Switch to enable adjustment of model discharge layer [0,1, I]

{{/SWITCH_SWDIVD_OPTION_1}}
{{/SWITCH_SWINTFL_OPTION_1}}
**********************************************************************************

{{#SWITCH_NRLEVS_OPTION_1}}
**********************************************************************************
* Part 3a: Drainage to level 1

  DRARES1 = {{DRARES1}}      ! Drainage resistance, [10..1d5 d, R]
  INFRES1 = {{INFRES1}}      ! Infiltration resistance, [0..1d5 d, R]

* Switch, for allowance drainage/infiltration:
  SWALLO1 = {{SWALLO1}}      ! 1 = Drainage and infiltration are both allowed
                             ! 2 = Drainage is not allowed
                             ! 3 = Infiltration is not allowed

{{#SWITCH_SWDIVD_OPTION_1}}
* If SWDIVD = 1 (drainage flux vertically distributed), specify the drain spacing:
  L1 = {{L1}}                ! Drain spacing, [1..100000 m, R]

{{/SWITCH_SWDIVD_OPTION_1}}
  ZBOTDR1 = {{ZBOTDR1}}      ! Level of drainage medium bottom, [-10000..0 cm, R]
  SWDTYP1 = {{SWDTYP1}}      ! Type of drainage medium: 1 = drain tube, 2 = open channel

* Specify date DATOWL1 [date] and channel water level LEVEL1 [-10000..200, cm, R]
* LEVEL1 is negative if below soil surface; maximum MAOWL records:

{{#TABLE_DATOWLTB1}}
  {{DATOWL1}} {{LEVEL1}}
{{/TABLE_DATOWLTB1}}
* End of table
{{/SWITCH_NRLEVS_OPTION_1}}
{{#SWITCH_NRLEVS_OPTION_2}}
**********************************************************************************
* Part 3a: Drainage to level 1

  DRARES1 = {{DRARES1}}      ! Drainage resistance, [10..1d5 d, R]
  INFRES1 = {{INFRES1}}      ! Infiltration resistance, [0..1d5 d, R]

* Switch, for allowance drainage/infiltration:
  SWALLO1 = {{SWALLO1}}      ! 1 = Drainage and infiltration are both allowed
                             ! 2 = Drainage is not allowed
                             ! 3 = Infiltration is not allowed

{{#SWITCH_SWDIVD_OPTION_1}}
* If SWDIVD = 1 (drainage flux vertically distributed), specify the drain spacing:
  L1 = {{L1}}                ! Drain spacing, [1..100000 m, R]

{{/SWITCH_SWDIVD_OPTION_1}}
  ZBOTDR1 = {{ZBOTDR1}}      ! Level of drainage medium bottom, [-10000..0 cm, R]
  SWDTYP1 = {{SWDTYP1}}      ! Type of drainage medium: 1 = drain tube, 2 = open channel

* Specify date DATOWL1 [date] and channel water level LEVEL1 [-10000..200, cm, R]
* LEVEL1 is negative if below soil surface; maximum MAOWL records:

{{#TABLE_DATOWLTB1}}
  {{DATOWL1}} {{LEVEL1}}
{{/TABLE_DATOWLTB1}}
* End of table

**********************************************************************************


**********************************************************************************
* Part 3b: Drainage to level 2

  DRARES2 = {{DRARES2}}      ! Drainage resistance, [10..1d5 d, R]
  INFRES2 = {{INFRES2}}      ! Infiltration resistance, [0..1d5 d, R]

* Switch, for allowance drainage/infiltration:
  SWALLO2 = {{SWALLO2}}      ! 1 = Drainage and infiltration are both allowed
                             ! 2 = Drainage is not allowed
                             ! 3 = Infiltration is not allowed

{{#SWITCH_SWDIVD_OPTION_1}}
* If SWDIVD = 1 (drainage flux vertically distributed), specify the drain spacing:
  L2 = {{L2}}                ! Drain spacing, [1..100000 m, R]

{{/SWITCH_SWDIVD_OPTION_1}}
  ZBOTDR2 = {{ZBOTDR2}}      ! Level of drainage medium bottom, [-10000..0 cm, R]
  SWDTYP2 = {{SWDTYP2}}      ! Type of drainage medium: 1 = drain tube, 2 = open channel

* Specify date DATOWL2 [date] and channel water level LEVEL2 [-10000..200, cm, R]
* LEVEL2 is negative if below soil surface; maximum MAOWL records:

{{#TABLE_DATOWLTB2}}
  {{DATOWL2}} {{LEVEL2}}
{{/TABLE_DATOWLTB2}}
* End of table
{{/SWITCH_NRLEVS_OPTION_2}}
{{#SWITCH_NRLEVS_OPTION_3}}
**********************************************************************************
* Part 3a: Drainage to level 1

  DRARES1 = {{DRARES1}}      ! Drainage resistance, [10..1d5 d, R]
  INFRES1 = {{INFRES1}}      ! Infiltration resistance, [0..1d5 d, R]

* Switch, for allowance drainage/infiltration:
  SWALLO1 = {{SWALLO1}}      ! 1 = Drainage and infiltration are both allowed
                             ! 2 = Drainage is not allowed
                             ! 3 = Infiltration is not allowed

{{#SWITCH_SWDIVD_OPTION_1}}
* If SWDIVD = 1 (drainage flux vertically distributed), specify the drain spacing:
  L1 = {{L1}}                ! Drain spacing, [1..100000 m, R]

{{/SWITCH_SWDIVD_OPTION_1}}
  ZBOTDR1 = {{ZBOTDR1}}      ! Level of drainage medium bottom, [-10000..0 cm, R]
  SWDTYP1 = {{SWDTYP1}}      ! Type of drainage medium: 1 = drain tube, 2 = open channel

* Specify date DATOWL1 [date] and channel water level LEVEL1 [-10000..200, cm, R]
* LEVEL1 is negative if below soil surface; maximum MAOWL records:

{{#TABLE_DATOWLTB1}}
  {{DATOWL1}} {{LEVEL1}}
{{/TABLE_DATOWLTB1}}
* End of table

**********************************************************************************


**********************************************************************************
* Part 3b: Drainage to level 2

  DRARES2 = {{DRARES2}}      ! Drainage resistance, [10..1d5 d, R]
  INFRES2 = {{INFRES2}}      ! Infiltration resistance, [0..1d5 d, R]

* Switch, for allowance drainage/infiltration:
  SWALLO2 = {{SWALLO2}}      ! 1 = Drainage and infiltration are both allowed
                             ! 2 = Drainage is not allowed
                             ! 3 = Infiltration is not allowed

{{#SWITCH_SWDIVD_OPTION_1}}
* If SWDIVD = 1 (drainage flux vertically distributed), specify the drain spacing:
  L2 = {{L2}}                ! Drain spacing, [1..100000 m, R]

{{/SWITCH_SWDIVD_OPTION_1}}
  ZBOTDR2 = {{ZBOTDR2}}      ! Level of drainage medium bottom, [-10000..0 cm, R]
  SWDTYP2 = {{SWDTYP2}}      ! Type of drainage medium: 1 = drain tube, 2 = open channel

* Specify date DATOWL2 [date] and channel water level LEVEL2 [-10000..200, cm, R]
* LEVEL2 is negative if below soil surface; maximum MAOWL records:

{{#TABLE_DATOWLTB2}}
  {{DATOWL2}} {{LEVEL2}}
{{/TABLE_DATOWLTB2}}
* End of table

**********************************************************************************


**********************************************************************************
* Part 3c: Drainage to level 3

  DRARES3 = {{DRARES3}}      ! Drainage resistance, [10..1d5 d, R]
  INFRES3 = {{INFRES3}}      ! Infiltration resistance, [0..1d5 d, R]

* Switch, for allowance drainage/infiltration:
  SWALLO3 = {{SWALLO3}}      ! 1 = Drainage and infiltration are both allowed
                             ! 2 = Drainage is not allowed
                             ! 3 = Infiltration is not allowed

{{#SWITCH_SWDIVD_OPTION_1}}
* If SWDIVD = 1 (drainage flux vertically distributed), specify the drain spacing:
  L3 = {{L3}}                ! Drain spacing, [1..100000 m, R]

{{/SWITCH_SWDIVD_OPTION_1}}
  ZBOTDR3 = {{ZBOTDR3}}      ! Level of drainage medium bottom, [-10000..0 cm, R]
  SWDTYP3 = {{SWDTYP3}}      ! Type of drainage medium: 1 = drain tube, 2 = open channel

* Specify date DATOWL3 [date] and channel water level LEVEL3 [-10000..200, cm, R]
* LEVEL3 is negative if below soil surface; maximum MAOWL records:

{{#TABLE_DATOWLTB3}}
  {{DATOWL3}} {{LEVEL3}}
{{/TABLE_DATOWLTB3}}
* End of table
{{/SWITCH_NRLEVS_OPTION_3}}
{{#SWITCH_NRLEVS_OPTION_4}}
**********************************************************************************
* Part 3a: Drainage to level 1

  DRARES1 = {{DRARES1}}      ! Drainage resistance, [10..1d5 d, R]
  INFRES1 = {{INFRES1}}      ! Infiltration resistance, [0..1d5 d, R]

* Switch, for allowance drainage/infiltration:
  SWALLO1 = {{SWALLO1}}      ! 1 = Drainage and infiltration are both allowed
                             ! 2 = Drainage is not allowed
                             ! 3 = Infiltration is not allowed

{{#SWITCH_SWDIVD_OPTION_1}}
* If SWDIVD = 1 (drainage flux vertically distributed), specify the drain spacing:
  L1 = {{L1}}                ! Drain spacing, [1..100000 m, R]

{{/SWITCH_SWDIVD_OPTION_1}}
  ZBOTDR1 = {{ZBOTDR1}}      ! Level of drainage medium bottom, [-10000..0 cm, R]
  SWDTYP1 = {{SWDTYP1}}      ! Type of drainage medium: 1 = drain tube, 2 = open channel

* Specify date DATOWL1 [date] and channel water level LEVEL1 [-10000..200, cm, R]
* LEVEL1 is negative if below soil surface; maximum MAOWL records:

{{#TABLE_DATOWLTB1}}
  {{DATOWL1}} {{LEVEL1}}
{{/TABLE_DATOWLTB1}}
* End of table

**********************************************************************************


**********************************************************************************
* Part 3b: Drainage to level 2

  DRARES2 = {{DRARES2}}      ! Drainage resistance, [10..1d5 d, R]
  INFRES2 = {{INFRES2}}      ! Infiltration resistance, [0..1d5 d, R]

* Switch, for allowance drainage/infiltration:
  SWALLO2 = {{SWALLO2}}      ! 1 = Drainage and infiltration are both allowed
                             ! 2 = Drainage is not allowed
                             ! 3 = Infiltration is not allowed

{{#SWITCH_SWDIVD_OPTION_1}}
* If SWDIVD = 1 (drainage flux vertically distributed), specify the drain spacing:
  L2 = {{L2}}                ! Drain spacing, [1..100000 m, R]

{{/SWITCH_SWDIVD_OPTION_1}}
  ZBOTDR2 = {{ZBOTDR2}}      ! Level of drainage medium bottom, [-10000..0 cm, R]
  SWDTYP2 = {{SWDTYP2}}      ! Type of drainage medium: 1 = drain tube, 2 = open channel

* Specify date DATOWL2 [date] and channel water level LEVEL2 [-10000..200, cm, R]
* LEVEL2 is negative if below soil surface; maximum MAOWL records:

{{#TABLE_DATOWLTB2}}
  {{DATOWL2}} {{LEVEL2}}
{{/TABLE_DATOWLTB2}}
* End of table

**********************************************************************************


**********************************************************************************
* Part 3c: Drainage to level 3

  DRARES3 = {{DRARES3}}      ! Drainage resistance, [10..1d5 d, R]
  INFRES3 = {{INFRES3}}      ! Infiltration resistance, [0..1d5 d, R]

* Switch, for allowance drainage/infiltration:
  SWALLO3 = {{SWALLO3}}      ! 1 = Drainage and infiltration are both allowed
                             ! 2 = Drainage is not allowed
                             ! 3 = Infiltration is not allowed

{{#SWITCH_SWDIVD_OPTION_1}}
* If SWDIVD = 1 (drainage flux vertically distributed), specify the drain spacing:
  L3 = {{L3}}                ! Drain spacing, [1..100000 m, R]

{{/SWITCH_SWDIVD_OPTION_1}}
  ZBOTDR3 = {{ZBOTDR3}}      ! Level of drainage medium bottom, [-10000..0 cm, R]
  SWDTYP3 = {{SWDTYP3}}      ! Type of drainage medium: 1 = drain tube, 2 = open channel

* Specify date DATOWL3 [date] and channel water level LEVEL3 [-10000..200, cm, R]
* LEVEL3 is negative if below soil surface; maximum MAOWL records:

{{#TABLE_DATOWLTB3}}
  {{DATOWL3}} {{LEVEL3}}
{{/TABLE_DATOWLTB3}}
* End of table

**********************************************************************************


**********************************************************************************
* Part 3d: Drainage to level 4

  DRARES4 = {{DRARES4}}      ! Drainage resistance, [10..1d5 d, R]
  INFRES4 = {{INFRES4}}      ! Infiltration resistance, [0..1d5 d, R]

* Switch, for allowance drainage/infiltration:
  SWALLO4 = {{SWALLO4}}      ! 1 = Drainage and infiltration are both allowed
                             ! 2 = Drainage is not allowed
                             ! 3 = Infiltration is not allowed

{{#SWITCH_SWDIVD_OPTION_1}}
* If SWDIVD = 1 (drainage flux vertically distributed), specify the drain spacing:
  L4 = {{L4}}                ! Drain spacing, [1..100000 m, R]

{{/SWITCH_SWDIVD_OPTION_1}}
  ZBOTDR4 = {{ZBOTDR4}}      ! Level of drainage medium bottom, [-10000..0 cm, R]
  SWDTYP4 = {{SWDTYP4}}      ! Type of drainage medium: 1 = drain tube, 2 = open channel

* Specify date DATOWL4 [date] and channel water level LEVEL4 [-10000..200, cm, R]
* LEVEL4 is negative if below soil surface; maximum MAOWL records:

{{#TABLE_DATOWLTB4}}
  {{DATOWL4}} {{LEVEL4}}
{{/TABLE_DATOWLTB4}}
* End of table
{{/SWITCH_NRLEVS_OPTION_4}}
{{#SWITCH_NRLEVS_OPTION_5}}
**********************************************************************************
* Part 3a: Drainage to level 1

  DRARES1 = {{DRARES1}}      ! Drainage resistance, [10..1d5 d, R]
  INFRES1 = {{INFRES1}}      ! Infiltration resistance, [0..1d5 d, R]

* Switch, for allowance drainage/infiltration:
  SWALLO1 = {{SWALLO1}}      ! 1 = Drainage and infiltration are both allowed
                             ! 2 = Drainage is not allowed
                             ! 3 = Infiltration is not allowed

{{#SWITCH_SWDIVD_OPTION_1}}
* If SWDIVD = 1 (drainage flux vertically distributed), specify the drain spacing:
  L1 = {{L1}}                ! Drain spacing, [1..100000 m, R]

{{/SWITCH_SWDIVD_OPTION_1}}
  ZBOTDR1 = {{ZBOTDR1}}      ! Level of drainage medium bottom, [-10000..0 cm, R]
  SWDTYP1 = {{SWDTYP1}}      ! Type of drainage medium: 1 = drain tube, 2 = open channel

* Specify date DATOWL1 [date] and channel water level LEVEL1 [-10000..200, cm, R]
* LEVEL1 is negative if below soil surface; maximum MAOWL records:

{{#TABLE_DATOWLTB1}}
  {{DATOWL1}} {{LEVEL1}}
{{/TABLE_DATOWLTB1}}
* End of table

**********************************************************************************


**********************************************************************************
* Part 3b: Drainage to level 2

  DRARES2 = {{DRARES2}}      ! Drainage resistance, [10..1d5 d, R]
  INFRES2 = {{INFRES2}}      ! Infiltration resistance, [0..1d5 d, R]

* Switch, for allowance drainage/infiltration:
  SWALLO2 = {{SWALLO2}}      ! 1 = Drainage and infiltration are both allowed
                             ! 2 = Drainage is not allowed
                             ! 3 = Infiltration is not allowed

{{#SWITCH_SWDIVD_OPTION_1}}
* If SWDIVD = 1 (drainage flux vertically distributed), specify the drain spacing:
  L2 = {{L2}}                ! Drain spacing, [1..100000 m, R]

{{/SWITCH_SWDIVD_OPTION_1}}
  ZBOTDR2 = {{ZBOTDR2}}      ! Level of drainage medium bottom, [-10000..0 cm, R]
  SWDTYP2 = {{SWDTYP2}}      ! Type of drainage medium: 1 = drain tube, 2 = open channel

* Specify date DATOWL2 [date] and channel water level LEVEL2 [-10000..200, cm, R]
* LEVEL2 is negative if below soil surface; maximum MAOWL records:

{{#TABLE_DATOWLTB2}}
  {{DATOWL2}} {{LEVEL2}}
{{/TABLE_DATOWLTB2}}
* End of table

**********************************************************************************


**********************************************************************************
* Part 3c: Drainage to level 3

  DRARES3 = {{DRARES3}}      ! Drainage resistance, [10..1d5 d, R]
  INFRES3 = {{INFRES3}}      ! Infiltration resistance, [0..1d5 d, R]

* Switch, for allowance drainage/infiltration:
  SWALLO3 = {{SWALLO3}}      ! 1 = Drainage and infiltration are both allowed
                             ! 2 = Drainage is not allowed
                             ! 3 = Infiltration is not allowed

{{#SWITCH_SWDIVD_OPTION_1}}
* If SWDIVD = 1 (drainage flux vertically distributed), specify the drain spacing:
  L3 = {{L3}}                ! Drain spacing, [1..100000 m, R]

{{/SWITCH_SWDIVD_OPTION_1}}
  ZBOTDR3 = {{ZBOTDR3}}      ! Level of drainage medium bottom, [-10000..0 cm, R]
  SWDTYP3 = {{SWDTYP3}}      ! Type of drainage medium: 1 = drain tube, 2 = open channel

* Specify date DATOWL3 [date] and channel water level LEVEL3 [-10000..200, cm, R]
* LEVEL3 is negative if below soil surface; maximum MAOWL records:

{{#TABLE_DATOWLTB3}}
  {{DATOWL3}} {{LEVEL3}}
{{/TABLE_DATOWLTB3}}
* End of table

**********************************************************************************


**********************************************************************************
* Part 3d: Drainage to level 4

  DRARES4 = {{DRARES4}}      ! Drainage resistance, [10..1d5 d, R]
  INFRES4 = {{INFRES4}}      ! Infiltration resistance, [0..1d5 d, R]

* Switch, for allowance drainage/infiltration:
  SWALLO4 = {{SWALLO4}}      ! 1 = Drainage and infiltration are both allowed
                             ! 2 = Drainage is not allowed
                             ! 3 = Infiltration is not allowed

{{#SWITCH_SWDIVD_OPTION_1}}
* If SWDIVD = 1 (drainage flux vertically distributed), specify the drain spacing:
  L4 = {{L4}}                ! Drain spacing, [1..100000 m, R]

{{/SWITCH_SWDIVD_OPTION_1}}
  ZBOTDR4 = {{ZBOTDR4}}      ! Level of drainage medium bottom, [-10000..0 cm, R]
  SWDTYP4 = {{SWDTYP4}}      ! Type of drainage medium: 1 = drain tube, 2 = open channel

* Specify date DATOWL4 [date] and channel water level LEVEL4 [-10000..200, cm, R]
* LEVEL4 is negative if below soil surface; maximum MAOWL records:

{{#TABLE_DATOWLTB4}}
  {{DATOWL4}} {{LEVEL4}}
{{/TABLE_DATOWLTB4}}
* End of table

**********************************************************************************


**********************************************************************************
* Part 3e: Drainage to level 5

  DRARES5 = {{DRARES5}}      ! Drainage resistance, [10..1d5 d, R]
  INFRES5 = {{INFRES5}}      ! Infiltration resistance, [0..1d5 d, R]

* Switch, for allowance drainage/infiltration:
  SWALLO5 = {{SWALLO5}}      ! 1 = Drainage and infiltration are both allowed
                             ! 2 = Drainage is not allowed
                             ! 3 = Infiltration is not allowed

{{#SWITCH_SWDIVD_OPTION_1}}
* If SWDIVD = 1 (drainage flux vertically distributed), specify the drain spacing:
  L5 = {{L5}}                ! Drain spacing, [1..100000 m, R]

{{/SWITCH_SWDIVD_OPTION_1}}
  ZBOTDR5 = {{ZBOTDR5}}      ! Level of drainage medium bottom, [-10000..0 cm, R]
  SWDTYP5 = {{SWDTYP5}}      ! Type of drainage medium: 1 = drain tube, 2 = open channel

* Specify date DATOWL5 [date] and channel water level LEVEL5 [-10000..200, cm, R]
* LEVEL5 is negative if below soil surface; maximum MAOWL records:

{{#TABLE_DATOWLTB5}}
  {{DATOWL5}} {{LEVEL5}}
{{/TABLE_DATOWLTB5}}
* End of table
{{/SWITCH_NRLEVS_OPTION_5}}
{{/SWITCH_DRAMET_OPTION_3}}

**********************************************************************************

* End of .dra file!
