# 文档至关重要

文档是代码库中最重要的部分之一；毕竟，如果除了作者之外没有人知道如何使用它，那么编写和发布软件又有什么意义呢？

很多次，在一段时间后重新开始一个项目的开发时，我发现自己不记得如何使用代码的某些部分。一旦代码库和贡献者团队不断壮大，这会变得更加棘手。

然而，我需要承认，编写文档所需的时间与编码一样长。而且，要跟上变化并不容易，对于开发人员来说，要意识到哪些地方解释得不好或根本没有解释也不容易。因此，用户体验和贡献至关重要。

贡献文档非常容易，并且与贡献代码的步骤相同，但您几乎不会破坏任何东西。此外，这是一种善意的姿态，超过 90% 的用户都会这样做。

如果您想支持该项目，但对 Python 不太熟悉，请查看文档并找出错误和需要改进的地方。如果您没有时间进行 fork、编辑和提交 PR，请至少创建一个带有 `#docs` 标签的问题。这将非常有帮助！

# 您可以在哪里贡献

pySWAP 文档可以分为两部分：API 参考（从代码中的 docstring 自动生成）和用户指南。

## 用户指南和 Wiki

在此文档中，我还放置了许多我认为从 SWAP 用户角度来看很重要的信息。即使您不使用 pySWAP，您也可以访问 wiki 并查看特定变量在模型中的作用。

这主要取自 SWAP 4.2 版提供的示例文件。

## API 参考

贡献 API 参考可能需要更多的技能。总的来说，在整个项目中，我们使用 Google 风格的文档。每个组件都使用 docstring 进行文档化。

### 模块文档

在每个模块的顶部，都有一个 docstring，它描述了该特定模块的作用以及其中定义了哪些元素。这是一个示例：

```Python
"""
SWAP 模拟的灌溉设置。

类：
    IrgFile: 灌溉文件。
    FixedIrrigation: 固定灌溉设置。
    ScheduledIrrigation: 灌溉调度设置。
    IRRIGATION: 每个固定灌溉事件的信息。

函数：
    irg_from_csv: 从 CSV 文件加载灌溉文件。
"""
```

通过阅读此内容，我们立即知道可以从该模块导入什么。这在您的代码编辑器中也有效；当您执行 `from pyswap import irrigation` 并将鼠标悬停在 `irrigation` 上时，您会在弹出窗口中看到该 docstring。

### 类和函数

Docstring 直接定义在类或函数定义下方。它们遵循标准的 Google 模式，如下所示：

```Python
class Irrigation:
    """保存模拟的作物设置。

    属性：
        swcrop (int): 作物开关：

            * 0 - 裸土。
            * 1 - 模拟作物。

        rds (Optional[float]): 作物根系深度 [cm]。
        table_croprotation (Optional[Table]): 作物轮作数据表。
        cropfiles (Optional[List[CropFile]]): 作物文件列表。

    方法：
        write_crop: 写入作物文件。
"""
    # 类的定义；属性等。
```

不过，有一些小技巧。

#### 描述开关选项

为了确保开关选项显示为列表，您必须在前后添加新行，以 1 个制表符缩进编写列表，并以 `*` 开头每行。

### 其他可重用元素

例如，如果您定义了一种新类型的字段，则其 docstring 直接位于变量定义下方，如下所示：

```Python
Arrays = Annotated[
    DataFrame,
    PlainSerializer(serialize_arrays, return_type=str, when_used="json")
]
"""将不带标题的 pd.DataFrame 序列化为带有前导变量名的字符串。"""
```

该 docstring 随后被代码编辑器中的检查工具使用，帮助用户了解其作用，并由 mkdocs 自动拉取以生成在线文档。
