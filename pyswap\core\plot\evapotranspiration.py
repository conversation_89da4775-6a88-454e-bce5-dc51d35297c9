"""绘制蒸散量（潜在与实际）并计算 RMSE。"""

import matplotlib.pyplot as plt
import seaborn as sns
from pandas import DataFrame


def evapotranspiration(
    potential: DataFrame, actual: DataFrame, title: str = "Evapotranspiration"
):
    """绘制蒸散量（潜在与实际）并计算 RMSE。

    参数：
        potential (DataFrame): 包含潜在蒸散量日期和值的 DataFrame。
        actual (DataFrame): 包含实际蒸散量日期和值的 DataFrame。
        title (str, optional): 图的标题。默认为“蒸散量”。
    """

    sns.set_context("poster")

    fig, ax = plt.subplots(figsize=(34, 8))
    sns.lineplot(data=potential, ax=ax, label="Potential", color="black", linewidth=1)
    sns.lineplot(
        data=actual, ax=ax, label="Actual", color="orange", linewidth=1, linestyle="--"
    )

    ax.set_title(title, pad=20)
    ax.set_xlabel("Date")
    ax.set_ylabel("Evapotranspiration")

    ax.tick_params(axis="x", rotation=45)
    ax.legend()
    plt.tight_layout()
    plt.show()
