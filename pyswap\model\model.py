# ruff: noqa: S603
# mypy: disable-error-code="override, func-returns-value, call-overload, operator, no-any-return"
# 覆盖错误与 validate 方法有关，可能与 Pydantic 有关。
# func-returns-value 在模型构建器上引发，因为它不返回任何内容。这不是一个优先修复的问题。
# 运算符在其中一个检查（swdra 比较）中返回，这是可以的。它在该点之前已经过验证。
# no-any-return 在 run_parallel 函数上引发，因为它返回一个结果列表。这不是一个优先修复的问题。
# 保护子进程调用似乎不是本项目中的优先事项
"""构建、运行和解析 SWAP 模型运行结果。

当 Model 类开始增长时，很明显它需要重构为更模块化的结构。
构建环境、运行和解析结果的功能已被抽象为 3 个类，
将主要（并暴露给用户）Model 类专注于模型组件及其交互。
此模块中的四个类是：

类：

    ModelBuilder: 负责构建模型组件的类。
    ModelRunner: 负责运行模型的类。
    ResultReader: 负责解析模型结果的类。
    Model: 运行 SWAP 模型的主类。
"""

from __future__ import annotations

import logging
import os
import shutil
import subprocess
import tempfile
import time
from multiprocessing import Pool
from pathlib import Path
from typing import Literal

from pandas import DataFrame, read_csv, to_datetime
from pydantic import Field, PrivateAttr, model_validator

from pyswap.components.boundary import BottomBoundary
from pyswap.components.crop import Crop
from pyswap.components.drainage import Drainage
from pyswap.components.irrigation import FixedIrrigation
from pyswap.components.metadata import Metadata
from pyswap.components.meteorology import Meteorology
from pyswap.components.simsettings import GeneralSettings, RichardsSettings
from pyswap.components.soilwater import (
    Evaporation,
    SnowAndFrost,
    SoilMoisture,
    SoilProfile,
    SurfaceFlow,
)
from pyswap.components.transport import HeatFlow, SoluteTransport
from pyswap.core.basemodel import PySWAPBaseModel
from pyswap.core.defaults import IS_WINDOWS
from pyswap.core.fields import Subsection
from pyswap.core.io.io_ascii import open_ascii
from pyswap.libs import swap_linux, swap_windows
from pyswap.model.result import Result
from pyswap.utils.mixins import FileMixin, SerializableMixin

logger = logging.getLogger(__name__)

__all__ = ["Model", "run_parallel"]


class ModelBuilder:
    """构建模型组件。

    属性：
        model (Model): 要构建的模型。
        tempdir (str): 存储输入文件的临时目录。

    方法：
        copy_executable: 将适当的 SWAP 可执行文件复制到临时目录。
        write_inputs: 将输入文件写入临时目录。
    """

    def __init__(self, model: Model, tempdir: str):
        self.model = model
        self.tempdir = tempdir

    def copy_executable(self) -> None:
        """将适当的 SWAP 可执行文件复制到临时目录。"""
        if IS_WINDOWS:
            shutil.copy(swap_windows, self.tempdir)
            logger.info(
                "正在将 SWAP 的 Windows 版本复制到临时目录..."
            )
        else:
            shutil.copy(swap_linux, self.tempdir)
            logger.info("正在将 Linux 可执行文件复制到临时目录...")

        return self

    def get_inputs(self) -> dict:
        """获取字典中的输入文件。"""
        inputs = {}

        inputs["swp"] = self.model.swp
        if self.model.lateraldrainage.swdra in [1, 2]:
            inputs["dra"] = self.model.lateraldrainage.drafile.dra
        if self.model.crop.cropfiles:
            inputs["crop"] = self.model.crop.cropfiles
        if self.model.meteorology.metfile:
            inputs["met"] = self.model.meteorology.met
        if self.model.fixedirrigation.swirgfil == 1:
            inputs["irg"] = self.model.fixedirrigation.irg
        if self.model.bottomboundary.swbbcfile == 1:
            inputs["bbc"] = self.model.bottomboundary.bbc

        return inputs

    def write_inputs(self) -> None:
        """将输入文件写入临时目录。"""
        logger.info("正在准备文件...")

        self.model.write_swp(self.tempdir)

        if self.model.lateraldrainage.swdra in [1, 2]:
            self.model.lateraldrainage.write_dra(self.tempdir)
        if self.model.crop.cropfiles:
            self.model.crop.write_crop(self.tempdir)
        if self.model.meteorology.metfile:
            self.model.meteorology.write_met(self.tempdir)
        if self.model.fixedirrigation.swirgfil == 1:
            self.model.fixedirrigation.write_irg(self.tempdir)
        if self.model.bottomboundary.swbbcfile == 1:
            self.model.bottomboundary.write_bbc(self.tempdir)

        return self


class ModelRunner:
    """负责运行模型的类。

    在 run 方法中，ResultReader 用于抽象模型结果的解析。

    属性：
        model (Model): 要运行的模型。

    方法：
        run_swap: 运行 SWAP 可执行文件。
        raise_swap_warning: 触发警告。
        run: 运行模型的主函数
    """

    def __init__(self, model: Model):
        self.model = model

    @staticmethod
    def run_swap(tempdir: Path) -> str:
        """Run the SWAP executable.

        在临时目录中运行可执行文件，并在可执行文件请求输入（终止时）时将换行符传递给 stdin。

        参数：
            tempdir (Path): 可执行文件存储的临时目录。
        """
        swap_path = Path(tempdir, "swap.exe") if IS_WINDOWS else "./swap420"
        p = subprocess.Popen(
            swap_path,
            stdout=subprocess.PIPE,
            stdin=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            cwd=tempdir,
        )
        stdout = p.communicate(input=b"\n")[0]

        return stdout.decode()

    def raise_swap_warning(self, warnings: list):
        """记录模型运行中的警告。

        参数：
            warnings (list): 通过 ResultReader 解析的模型运行警告。
        """
        for message in warnings:
            logger.warning(message)

    def run(self, path: str | Path, silence_warnings: bool = False) -> Result:
        """运行模型的主函数。

        首先使用 ModelBuilder 准备模型运行环境。
        其次，运行 SWAP 可执行文件，并使用 ResultReader 解析从可执行文件传递的解码结果，
        然后用于更新 Result 对象。

        参数：
            path (str | Path): 临时目录的路径。
            silence_warnings (bool): 如果为 True，则不触发警告。

        返回：
            Result: 解析后的模型结果。
        """

        with tempfile.TemporaryDirectory(dir=path) as tempdir:
            builder = ModelBuilder(self.model, tempdir)
            builder.copy_executable().write_inputs()

            stdout = self.run_swap(tempdir)

            if "normal completion" not in stdout:
                msg = f"Model run failed. \n {stdout}"
                raise RuntimeError(msg)

            logger.info(stdout)

            # --- Handle the results ---
            result: Result = Result()

            reader = ResultReader(self.model, tempdir)

            log = reader.read_swap_log()
            result.log = log

            warnings = reader.identify_warnings(log)
            result.warning = warnings

            if warnings and not silence_warnings:
                self.raise_swap_warning(warnings=warnings)

            if "csv" in self.model.generalsettings.extensions:
                output = reader.read_csv_output(which="csv")
                result.output.update({"csv": output})

            if "csv_tz" in self.model.generalsettings.extensions:
                output_tz = reader.read_csv_output(which="csv_tz")
                result.output.update({"csv_tz": output_tz})

            ascii_files = reader.read_ascii_output()

            result.output.update(ascii_files)
            time.sleep(0.1) # 添加短暂延迟以确保文件句柄释放
            return result


class ResultReader:
    """负责读取模型结果的类。

    属性：
        model (Model): 要读取结果的模型。
        tempdir (str): 存储结果的临时目录。

    方法：
        read_csv_output: 读取 CSV 输出。
        read_swap_log: 读取日志文件。
        identify_warnings: 从日志文件中捕获警告。
        read_ascii_output: 将所有非 CSV 格式的输出文件读取为字符串。
    """

    def __init__(self, model: Model, tempdir: str):
        self.model: Model = model
        self.tempdir = tempdir

    def read_csv_output(self, which: Literal["csv", "csv_tz"]) -> DataFrame:
        """读取 CSV 输出。

        有两种类型的 CSV 输出文件：csv 和 csv_tz。它们都通过模式更改在相同的方法中处理。

        参数：
            which (str): 要读取的输出文件类型。

        返回：
            DataFrame: 作为 DataFrame 的输出文件。
        """

        outfil = self.model.generalsettings.outfil
        output_suffix = "_output.csv" if which == "csv" else "_output_tz.csv"
        index_col = "DATETIME" if which == "csv" else "DATE"

        path = Path(self.tempdir, outfil + output_suffix)

        if not path.exists():
            logger.warning(f"Expected output file {path} not found.")
            return DataFrame()

        df = read_csv(path, comment="*", index_col=index_col)
        df.index = to_datetime(df.index)

        return df

    def read_swap_log(self) -> str:
        """读取日志文件。

        返回：
            str: 日志文件的内容。

        引发：
            FileNotFoundError: 如果未找到日志文件。应该始终存在日志文件。如果不是，则表示出现问题。
            FileExistsError: 如果找到多个日志文件。不确定这是否可能。如果可能，则应进行处理。
        """

        log_files = [
            f for f in Path(self.tempdir).glob("*.log") if f.name != "reruns.log"
        ]

        if len(log_files) == 0:
            msg = "No .log file found in the directory."
            raise FileNotFoundError(msg)

        elif len(log_files) > 1:
            msg = "Multiple .log files found in the directory."
            raise FileExistsError(msg)

        log_file = log_files[0]

        with open(log_file) as file:
            log_content = file.read()

        return log_content

    @staticmethod
    def identify_warnings(log: str) -> list:
        """从日志文件中捕获警告。

        ModelRunner 使用此方法在模型运行后触发警告。

        参数：
            log (str): 日志文件内容。

        返回：
            list: 警告列表。
        """
        lines = log.split("\n")
        warnings = [
            line for line in lines if line.strip().lower().startswith("warning")
        ]
        return warnings

    def read_ascii_output(self):
        """将所有非 CSV 格式的输出文件读取为字符串。

        此方法可能有点过于简化。将来，我们可能会考虑为不同的输出文件引入解析器。
        目前，我们只是将它们读取为字符串。

        返回：
            dict (dict): 以扩展名为键的输出字符串字典。
        """

        ascii_extensions = [
            ext
            for ext in self.model.generalsettings.extensions
            if ext not in ["csv", "csv_tz"]
        ]

        list_dir = os.listdir(self.tempdir)
        list_dir = [f for f in list_dir if f.endswith(tuple(ascii_extensions))]

        if list_dir:
            dict_files = {
                f.split(".")[1]: open_ascii(Path(self.tempdir, f)) for f in list_dir
            }
            return dict_files
        return {}


class Model(PySWAPBaseModel, FileMixin, SerializableMixin):
    """运行 SWAP 模型的主类

    即使所有部分都设置为可选，如果缺少任何组件，模型也将无法运行

    属性：
        metadata (Subsection): 模型的元数据
        version (str): 模型的版本
        generalsettings (Subsection): 模拟设置
        meteorology (Subsection): 气象数据
        crop (Subsection): 作物数据
        fixedirrigation (Subsection): 固定灌溉设置
        soilmoisture (Subsection): 土壤水分数据
        surfaceflow (Subsection): 地表径流数据
        evaporation (Subsection): 蒸发数据
        soilprofile (Subsection): 土壤剖面数据
        snowandfrost (Subsection): 积雪和霜冻数据
        richards (Subsection): Richards 数据
        lateraldrainage (Subsection): 侧向排水数据
        bottomboundary (Subsection): 底部边界数据
        heatflow (Subsection): 热流数据
        solutetransport (Subsection): 溶质传输数据

    方法：
        write_swp: 写入 .swp 输入文件。
        validate: 验证模型。
        run: 运行模型。
    """

    _validate_on_run: bool = PrivateAttr(default=False)
    _extension = "swp"

    metadata: Subsection[Metadata] | None = Field(default=None, repr=False)
    version: str = Field(exclude=True, default="base")
    generalsettings: Subsection[GeneralSettings] | None = Field(
        default=None, repr=False
    )
    meteorology: Subsection[Meteorology] | None = Field(default=None, repr=False)
    crop: Subsection[Crop] | None = Field(default=None, repr=False)
    fixedirrigation: Subsection[FixedIrrigation] | None = Field(
        default=FixedIrrigation(swirfix=0), repr=False
    )
    soilmoisture: Subsection[SoilMoisture] | None = Field(default=None, repr=False)
    surfaceflow: Subsection[SurfaceFlow] | None = Field(default=None, repr=False)
    evaporation: Subsection[Evaporation] | None = Field(default=None, repr=False)
    soilprofile: Subsection[SoilProfile] | None = Field(default=None, repr=False)
    snowandfrost: Subsection[SnowAndFrost] | None = Field(
        default=SnowAndFrost(swsnow=0, swfrost=0), repr=False
    )
    richards: Subsection[RichardsSettings] | None = Field(
        default=RichardsSettings(swkmean=1, swkimpl=0), repr=False
    )
    lateraldrainage: Subsection[Drainage] | None = Field(default=None, repr=False)
    bottomboundary: Subsection[BottomBoundary] | None = Field(default=None, repr=False)
    heatflow: Subsection[HeatFlow] | None = Field(default=HeatFlow(swhea=0), repr=False)
    solutetransport: Subsection[SoluteTransport] | None = Field(
        default=SoluteTransport(swsolu=0), repr=False
    )

    @property
    def swp(self):
        """SWP 文件的内容

        Subsection 字段类型的序列化已设置为在父类上调用 `model_string()` 时生成 SWAP 格式的字符串
        """
        return self.model_string()

    @model_validator(mode="after")
    def validate_missing_components(self):
        """在运行时验证所有必需组件是否存在。"""

        if not self._validate_on_run:
            return self

        required_components = [
            "metadata",
            "generalsettings",
            "meteorology",
            "crop",
            "fixedirrigation",
            "soilmoisture",
            "surfaceflow",
            "evaporation",
            "soilprofile",
            "snowandfrost",
            "richards",
            "lateraldrainage",
            "bottomboundary",
            "heatflow",
            "solutetransport",
        ]

        missing_components = [
            comp for comp in required_components if getattr(self, comp) is None
        ]

        if missing_components:
            msg = f"Missing required components: {', '.join(missing_components)}"
            raise ValueError(msg)

        # 验证每个组件
        for comp in required_components:
            getattr(self, comp)

        return self

    @model_validator(mode="after")
    def validate_each_component(self):
        """在运行时验证所有必需组件是否存在。"""

        if not self._validate_on_run:
            return self

        for comp in self.model_fields:
            item = getattr(self, comp)
            if hasattr(item, "validate_with_yaml"):
                item._validation = True
                item.validate_with_yaml()

        return self

    def validate(self):
        """在调用 `run()` 时执行模型验证。

        此方法可能需要重构。它似乎遮蔽了 Pydantic 中的某些验证方法。
        """

        try:
            self._validate_on_run = True
            self.model_validate(self)
        finally:
            self._validate_on_run = False
            logger.info("Validation successful.")

    def write_swp(self, path: str | Path):
        """写入 .swp 输入文件。

        参数：
            path (str | Path): 写入文件的路径。
        """
        self.save_file(string=self.swp, path=path, fname="swap")

    def get_inputs(self) -> dict:
        """获取字典中的输入文件。"""
        builder = ModelBuilder(model=self, tempdir=Path.cwd())
        return builder.get_inputs()

    def to_classic_swap(self, path: Path) -> None:
        """在用户目录中准备模型运行的所有文件。"""
        self.validate()
        builder = ModelBuilder(model=self, tempdir=path)

        builder.write_inputs()
        builder.copy_executable()

        logger.info(f"Model files written to {path}")

    def run(
        self, path: str | Path | None = None, silence_warnings: bool = False
    ) -> Result:
        """使用 ModelRunner 运行模型。"""
        self.validate()
        path = Path.cwd() if path is None else path
        return ModelRunner(self).run(path, silence_warnings)


def _run_model_with_params(args) -> Result:
    """运行带参数模型的辅助函数。"""
    model, path, silence_warnings = args
    return model.run(path=path, silence_warnings=silence_warnings)


def run_parallel(
    mls: list[Model],
    path: Path | str | None = None,
    silence_warnings: bool = False,
    **kwargs,
) -> list[Result]:
    """并行运行多个模型。

    参数：
        mls (list[Model]): 要运行的模型列表。
        path (Path | str): 临时目录的路径。
        silence_warnings (bool): 如果为 True，则不触发警告。
        **kwargs (dict): Pool() 的关键字参数。

    返回：
        list[Result]: 模型运行结果列表。
    """
    with Pool(**kwargs) as pool:
        results = pool.map(
            _run_model_with_params, [(model, path, silence_warnings) for model in mls]
        )

    return results
