# mypy: disable-error-code="attr-defined"
# 由于某种原因，在基于数据透视表列（即日期）定义月份标签时引发了错误。
# 暂时禁用此错误。我们将观察是否出现任何错误。

"""
函数：
    water_content: 将含水量绘制为热图。
"""

import pandas as pd
import seaborn as sns
from matplotlib import pyplot as plt


def water_content(
    df: pd.DataFrame,
    depth_col: str,
    date_col: str,
    wcontent_col: str,
    title: str = "Water content",
):
    """将含水量绘制为热图。

    为了使此函数正常工作，用户应使用转换为数据框的 `vap` 输出，
    或者确保在他们提供的 csv_tz 输出中，只包含含水量数据。

    参数：
        df (pd.DataFrame): 包含含水量数据的 DataFrame
        depth_col (str): 深度数据的列名
        date_col (str): 日期数据的列名
        wcontent_col (str): 含水量数据的列名
        title (str, optional): 图的标题。默认为“含水量”。
    """

    sns.set_context("poster")

    df_wcont = df[[depth_col, date_col, wcontent_col]]

    df_wcont.loc[:, date_col] = pd.to_datetime(df_wcont[date_col])
    df_wcont.loc[:, depth_col] = df_wcont[depth_col].astype(float)
    df_wcont.loc[:, wcontent_col] = df_wcont[wcontent_col].astype(float)

    pivot_table = df_wcont.pivot(columns=date_col, index=depth_col, values=wcontent_col)
    pivot_table = pivot_table.sort_index(axis=1)

    plt.figure(figsize=(34, 8))
    sns.heatmap(pivot_table, cmap="YlGnBu")
    plt.title(title)
    plt.xlabel("Date")
    plt.ylabel("Depth (cm)")

    plt.gca().invert_yaxis()

    plt.xticks(rotation=45)

    format_months = lambda x, p: pivot_table.columns[int(x)].strftime("%Y-%m")
    plt.gca().xaxis.set_major_formatter(plt.FuncFormatter(format_months))
