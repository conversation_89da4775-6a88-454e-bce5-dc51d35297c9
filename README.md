# pySWAP - SWAP 水文模型的 Python 包装器

[![Tests](https://github.com/zawadzkim/pySWAP/actions/workflows/tests.yaml/badge.svg)](https://github.com/zawadzkim/pySWAP/actions/workflows/tests.yaml)
[![codecov](https://codecov.io/gh/zawadzkim/pySWAP/graph/badge.svg?token=TG8KU0S6PM)](https://codecov.io/gh/zawadzkim/pySWAP)
[![pypi](https://img.shields.io/pypi/v/pySWAP.svg)](https://pypi.python.org/pypi/pySWAP)

[![downloads](https://static.pepy.tech/badge/pySWAP/month)](https://pepy.tech/project/pySWAP)
[![versions](https://img.shields.io/pypi/pyversions/pySWAP.svg)](https://pypi.python.org/pypi/pySWAP)

[![Binder](https://mybinder.org/badge_logo.svg)](https://mybinder.org/v2/gh/zawadzkim/pySWAP/notebooks)
[![DOI](https://zenodo.org/badge/757418278.svg)](https://doi.org/10.5281/zenodo.14884487)


pySWAP 是 SWAP 水文模型（[模型网站链接](https://www.swap.alterra.nl/)）的 Python 包装器（而非 Python 实现）。它简化了输入文件的创建、SWAP 模型的执行以及结果的分析和可视化。用户可以在 Jupyter Notebook 中设置和记录他们的模型，从而提高透明度、协作性，并促进社区支持的调试。

## Contributing

pySWAP 处于开发的早期阶段，因此非常鼓励任何贡献。您可以在 GitHub 上提出问题、提交拉取请求或发起讨论。有关如何贡献的更多详细信息，请访问 [CONTRIBUTE](./docs/CONTRIBUTING.md) 部分并参与进来！

## Help

有关详细说明，请查阅 pySWAP [文档](https://zawadzkim.github.io/pySWAP/) 页面。

## Installation

安装此包最简单的方法是通过 pip：

```shell
pip install pyswap
```

您也可以从 github 克隆仓库：

```shell
git clone --recurse-submodules https://github.com/zawadzkim/pySWAP.git
```

请注意，有一个 recurse-submodules 标志，它确保也克隆了额外的库。
