{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Basic usage\n", "\n", "Welcome to the basic usage notebook of pySWAP package. Here we will go through the basics of how a SWAP model is constructed in pySWAP, load one of the test case models and explore it a bit."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pyswap as psp\n", "\n", "psp.log.set_log_level(\"WARNING\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["pySWAP is an object-oriented library, which means that we try to split and encapsulate distinct parts of the SWAP model setup as objects. These objects need to be defined by the user at the initial stage and then can later be easily manipulated, copied and compared. Below is an example of the Meteorological settings object:"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## <PERSON><PERSON><PERSON><PERSON> testcase\n", "\n", "A lot of the model settings still need to be explicitly written out. There is, however, an ongoing effort to reduce to minimum the amount of code that has to be written for the initial model. If you are interested how the Hupselbrook model is implemented in pySWAP, take a look at file `hupselbrook.py` in `testcase` module.\n", "\n", "Let's load the model and check the metadata."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ml = psp.testcase.get(\"hups<PERSON><PERSON>\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["`Model` object has a method run, which runs the model and captures the results into a `Result` object. The only argument that needs to be passed is the path to where the temporary directory can be created."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(ml.swp)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["result = ml.run()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["From now on, we can use Result object's attributes and computed fields to access the results. The .csv, for example, is already loaded as DataFrame."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["result.output.keys()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["result.yearly_summary"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(result.blc_summary)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["result.csv[[\"RAIN\", \"IRRIG\", \"INTERC\"]].plot()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Alternative way to load a model\n", "\n", "Since pyswap 0.2.0, it is possible to load an entire model from the classic ascii configuration files. In order to do that, you need to create the swp and other relevant files separately and then put them together in a model. You can see it in the tutorial on [loading classic SWAP model](/tutorials/loading-classic-swap)."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Changing the model settings\n", "\n", "Assume you would like to add another file extension to be generated, or make any other change for that matter. The best way to achieve this is to create a copy of a component you want to alter, and then update the model with that new component, saving it's copy."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# updated general settings including inlist_csv_tz\n", "ml.generalsettings.extensions = [*ml.generalsettings.extensions, \"csv_tz\"]\n", "ml.generalsettings.inlist_csv_tz = [\"WC\"]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["result2 = ml.run()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from pyswap.core.plot import water_content\n", "\n", "wc_df = result2.csv_tz.reset_index()\n", "\n", "water_content(wc_df, \"DEPTH\", \"DATE\", \"WC\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Alternative way to setup the model\n", "\n", "Initially it was only possible to directly assign generated model components to the Model object attributes. Since the newest release (February 2025) it is possible to set up and run the model by loading the plain text files used traditionally to set up the model. See [this notebook](/tutorials/004-loading-classic-swap/) to see how to do it!"]}], "metadata": {"kernelspec": {"display_name": "pyswap-U62T3krn-py3.11", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 2}