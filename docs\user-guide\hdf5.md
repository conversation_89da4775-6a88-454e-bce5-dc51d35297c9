# HDF5 数据库

保留生成的模型对于此包至关重要。考虑了几种技术，主要关注：

- 广泛使用
- 简单结构
- 跨平台兼容性
- 编程语言独立性

最初，SQLite 被认为是简单表格数据的不错选择。然而，SWAP 多样化的输入和输出格式将对 SQLite 构成挑战，需要非常规地使用 RDBMS。为什么要强行将圆柱体穿过方孔呢？

HDF5 文件格式是科学领域广泛使用的更好替代方案。它几乎接受任何格式，与多种语言（R、Python、Fortran）兼容，并且具有简单的文件夹状结构。因此，pySWAP 是基于 `h5py` 库构建的。

目标是使 HDF5 数据库易于与使用不同编程语言的模型人员共享。目前，pySWAP 使用 pickle 来保存和检索 Python 特定的二进制格式。但是，可以根据社区需求添加更多格式。使用带有某些元数据的 pickle 对象是一种非常直接的方式，可以轻松存储和检索整个模型及其结果。要演示其工作原理，请遵循 [HDF5 数据库教程](/tutorials/002-hdf5-database/)。
