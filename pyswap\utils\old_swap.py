# ruff: noqa: C901
# mypy: disable-error-code="call-overload, attr-defined"
# parse_ascii_file 函数引发了 C901 错误。该函数确实可以重构，但目前我们保持原样。
# attr-defined 错误导致 __name__ dunder 方法出现问题，这是错误的。
# create schema 函数调用时引发了 call overload 错误。
import inspect
import re

import pandas as pd
import pandera as pa

import pyswap.components.tables as tables
from pyswap.core.basemodel import BaseTableModel


def remove_comments(text: str) -> str:
    """Remove comments from a SWAP input file.

    在 SWAP 输入文件中，有些行是注释。整行注释以 * 字符开头。部分注释以 ! 字符开头，有时跟在实际数据后面。

    !!! note
        在此阶段不删除空行。它们对于解析表格很重要。

    参数：
        text (str)：要从中删除注释的文本。

    返回：
        str：删除注释后的修剪文本。
    """
    # 移除以 * 开头的行
    text = re.sub(r"^\*.*$", "", text, flags=re.MULTILINE)
    # 移除每行中 ! 之后的所有内容
    text = re.sub(r"!.*", "", text)

    return text.strip()


def parse_ascii_file(file_content) -> dict[str, dict]:
    """Parse an ASCII file in SWAP format.

    !!! note "假设"
        - 键值对是包含单个 `=` 字符的行
        - 表格是列由空格分隔的行
        - 空标签是末尾带有 `=` 字符的行，后跟以下行中的表格状数据。
        - 表格后跟空行或不属于另一个表格的行。

    参数：
        file_content (str)：ASCII 文件的内容。

    返回：
        dict：一个包含键值对、数组和表格的字典（按精确顺序）。
    """
    lines = file_content.splitlines()
    pairs = {}
    arrays = {}
    tables = {}

    def is_key_value(line):
        return (
            "=" in line
            and not line.strip().startswith("=")
            and not line.strip().endswith("=")
        )

    def format_key_value(line):
        key, value = line.split("=", 1)
        return {key.strip().lower(): value.strip()}

    def is_table(line):
        """检查行是否是表格的一部分。

        表格本质上是除键值对或空标签之外的所有内容，除了空行。
        """
        return line.strip() and "=" not in line and not line.strip().endswith("=")

    def is_empty_tag(line):
        """检查行是否为空标签。

        空标签是指只有标签后跟等号（例如，DZNEW =）的行，并且该标签的数据在下一行或多行中。这对于表格最常见，在 pySWAP 中称为 ARRAYS - 没有标题但值按空格分隔的列分组的表格。"""

        return line.strip().endswith("=")

    def parse_table(lines, start_index, key):
        """从行列表中解析表格。

        如果检测到空标签或表格，则触发此函数。它将假定空标签或表格标题后的所有行都是表格的一部分，直到找到空行或不属于表格的行。然后将这些行存储在列表中，稍后用于在解析下一个项目之前跳过表格行。
        """
        data = []
        for line in lines[start_index:]:
            if line.strip() and not is_key_value(line) and not is_empty_tag(line):
                data.append(line.strip().split())
            else:
                break
        return {tuple(key.strip().split()): data}

    i = 0
    # 遍历行列表，并去除每行的空白字符
    while i < len(lines):
        line = lines[i].strip()

        if is_key_value(line):
            pairs.update(format_key_value(line))

        elif is_empty_tag(line):
            key = line[:-1].strip()
            array = parse_table(lines, i + 1, key)
            arrays.update(array)
            # 这是旧的实现：
            # i += len(list(array.values())[0]) + 1
            i += len(next(iter(array.values()))) + 1  # 跳过标签数据

        elif is_table(line):
            table = parse_table(lines, i + 1, line)
            tables.update(table)
            # 这是旧的实现：
            # i += len(list(array.values())[0]) + 1
            i += len(next(iter(table.values()))) + 1  # 跳过表格行
        i += 1  # 移动到下一行

    return pairs, arrays, tables


def is_dataframe_schema(member) -> bool:
    """检查成员是否是类，而不是 pd.Series 或 BaseTableModel 的子类。

    参数：
        member (Any)：要检查的成员。
    """
    cond = (
        inspect.isclass(member)
        and not issubclass(member, pd.Series)
        and member is not BaseTableModel
    )
    return cond


def get_schemas_with_columns() -> list[dict]:
    """创建一个包含表名、类名和列名的字典列表。"""
    members = inspect.getmembers(tables, is_dataframe_schema)
    members_with_columns = [
        {"name": v[0], "class": v[1], "cols": tuple(v[1].to_schema().columns.keys())}
        for v in members
    ]
    return members_with_columns


def match_schema_by_columns(data_columns: tuple, schema_columns: tuple) -> bool:
    """检查数据列是否是模式列的子集。

    参数：
        data_columns (tuple)：从 ASCII 文件解析的数据中的列名元组。
        schema_columns (tuple)：模式中的列名元组。
    """
    return frozenset(data_columns).issubset(frozenset(schema_columns["cols"]))


def create_schema_object(
    schema: BaseTableModel, columns: list, data: list
) -> BaseTableModel:
    """从数据列表中创建模式对象。

    参数：
        schema (BaseTableModel)：用于验证数据的模式类。
        columns (list)：列名列表。
        data (list)：要验证的数据列表。
    """
    df = pd.DataFrame(data, columns=columns)
    try:
        schema_object = schema.validate(df)
    except pa.errors.SchemaError as e:
        msg = f"Validation error for {schema.__name__}: {e!s}"
        print(msg)
        return None
    else:
        return schema_object


def create_table_objects(data_dict: dict) -> dict:
    """创建表格对象。

    参数：
        data_dict (dict)：一个字典，其中键是表名（要匹配的列名元组）。

    返回：
        dict：一个字典，其中键是表名，值是经过验证的模式对象。
    """
    schemas = get_schemas_with_columns()
    table_objects = {}

    for key, value in data_dict.items():
        if not isinstance(key, tuple):
            continue

        for schema in schemas:
            if match_schema_by_columns(key, schema):
                table_objects[schema["name"].lower()] = create_schema_object(
                    schema["class"], key, value
                )
                break

    return table_objects


def create_array_objects(data_dict: dict, grass_crp: bool = False) -> dict:
    """通过匹配数组名称（元组中的位置 0）创建数组对象

    参数：
        data_dict (dict)：一个字典，其中键是数组名称（要匹配的列名元组）。
        grass_crp (bool)：数组是否为草作物。如果数组是草作物，则此参数用于从列集中删除 DVS 列。否则，将删除 DNR 列。这是因为草模块依赖于天数而不是发育阶段。
    """
    schemas = get_schemas_with_columns()
    array_objects = {}

    for key, value in data_dict.items():
        for schema in schemas:
            data_item_name = key[0].lower() if isinstance(key, tuple) else key.lower()
            if data_item_name == schema["name"].lower():
                # 如果数组是草作物，则从集合中移除 DVS 列。
                # 否则移除 DNR 列。这样做是为了仍然提供数据验证，并保持通过模式名称匹配参数的想法。

                if grass_crp:
                    schema["cols"] = tuple(
                        col for col in schema["cols"] if col.upper() != "DVS"
                    )
                else:
                    schema["cols"] = tuple(
                        col for col in schema["cols"] if col.upper() != "DNR"
                    )
                array_objects[schema["name"].lower()] = create_schema_object(
                    schema["class"], schema["cols"], value
                )
                break

    return array_objects
