# mypy: disable-error-code="attr-defined, no-any-return, index"
# attr-defined 错误在 wofost mixin 中调用 .update 方法时出现。
# 最简单的修复方法是检查 PySWAPBaseModel 实例，但这会创建循环导入问题，我不想处理。目前这不是一个优先修复的问题。
# no-any-return 错误在 get_origin 方法中引发。这不是一个优先修复的问题。
# 索引错误也一样；它抱怨 FieldInfo 对象不可索引，但实际上在这种情况下它应该是可索引的，因为有 Optional 或 Annotated 类型。

"""可重用的混入，增强特定 PySWAPBaseModel 的功能。

为了保持主要的 PySWAPBaseModel 类和组件库的整洁和专注，混入用于向需要额外功能的类添加功能。
混入的概念受到 Django 框架的启发，它确实有助于保持代码的整洁和组织。

如果将来一个或多个类需要更多功能，则应将其实现为混入，然后由需要它的类继承。

类：

    FileMixin: 为需要文件 I/O 的模型提供自定义保存功能。
    SerializableMixin: 将模型转换为 SWAP 格式的字符串。
    YAMLValidatorMixin: 使用外部 YAML 规则验证参数。
    WOFOSTUpdateMixin: pySWAP 的 WOFOST 作物参数数据库接口。
"""

from pathlib import Path
from typing import Any, Literal, Self, Union, get_args, get_origin

from pydantic import BaseModel, PrivateAttr, model_serializer, model_validator
from pydantic.fields import FieldInfo

from pyswap.core.defaults import VALIDATIONRULES
from pyswap.log import logging

logger = logging.getLogger(__name__)


class FileMixin:
    """为需要文件 I/O 的模型提供自定义保存功能。
    
    !!! 注意：
    
            _extension 属性应在继承此混入的类中设置。建议使用 pydantic 的 PrivateAttr 来向用户隐藏此属性。
    
        方法：
            save_file: 将字符串保存到文件。
    """

    def save_file(
        self,
        string: str,
        fname: str,
        path: Path,
    ) -> None:
        """将字符串保存到文件。
        
                现在，每个继承此混入的类都应将扩展名作为私有属性提供。
        
                参数：
                    string: 要保存到文件的字符串。
                    fname: 文件名。
                    path: 文件应保存的路径。
        """

        if not hasattr(self, "_extension"):
            msg = "必须设置 _extension 属性。"
            raise AttributeError(msg)

        ext = self._extension
        fname = f"{fname}.{ext}" if ext else fname

        with open(f"{path}/{fname}", "w", encoding="ascii") as f:
            f.write(string)

        logger.info(f"{fname} saved successfully.")

        return None


class SerializableMixin(BaseModel):
    """将模型转换为 SWAP 格式的字符串。
    
        此混入仅由直接序列化为 SWAP 格式字符串的类继承。假设继承类：
    
        - 不包含嵌套类。
        - 如果类包含嵌套类，则应使用 Subsection 字段类型或覆盖 `model_string()` 方法。
    
        方法：
            if_is_union_type: 检查字段类型是否为 Union 类型。
            is_annotated_exception_type: 检查属性类型是否为 Table、Arrays 或 ObjectList。
            serialize_model: 覆盖默认的序列化方法。
            model_string: 将字典中的格式化字符串连接成一个字符串。
    """

    def if_is_union_type(self, field_info: FieldInfo) -> dict | None:
        """检查字段类型是否为 Union 类型。
        
                如果是，则在 Union 类型的第一个参数的 field_info 中查找 json_schema_extra 属性。
                如果未找到，则返回 None。这在例如可选类（如 Union[Table, None]）的情况下是必需的。
        
                ��数：
                    field_info (FieldInfo): 字段的 FieldInfo 对象。
        """

        field_type = field_info.annotation

        if get_origin(field_type) is Union:
            union_args = get_args(field_type)
            args = get_args(union_args[0])

            field_info = [item for item in args if isinstance(item, FieldInfo)]

            if not field_info:
                return None

            # 只返回 json_schema_extra 属性。这在某些情况下用于从 pyswap.core.fields 模块中的序列化器向 model_dump 传递附加信息。
            return field_info[0].json_schema_extra
        return None

    def is_annotated_exception_type(self, field_name: str) -> bool:
        """检查属性类型是否为 Table、Arrays 或 ObjectList。
        
                对于 Table、Arrays 和 ObjectList 类型，返回 True，确保单独的序列化路径。
        
                首先尝试从 Union 类型分配 json_schema_extra。如果失败，则从 field_info 分配 json_schema_extra。
                如果 json_schema_extra 为 None，则返回 False。
        """
        # 每个特殊字段都将有一个 FieldInfo 对象
        field_info = self.model_fields.get(field_name, None)

        if field_info is None:
            return False

        json_schema_extra = (
            self.if_is_union_type(field_info) or field_info.json_schema_extra
        )

        if json_schema_extra is None:
            return False

        return json_schema_extra.get("is_annotated_exception_type", False)

    @model_serializer(when_used="json", mode="wrap")
    def serialize_model(self, handler: Any):
        """覆盖默认的序列化方法。
        
                在中间步骤中，创建一个包含 SWAP 格式字符串的字典。
        """
        result = {}
        validated_self = handler(self)
        for field_name, field_value in validated_self.items():
            if self.is_annotated_exception_type(field_name):
                result[field_name] = field_value
            else:
                result[field_name] = f"{field_name.upper()} = {field_value}"
        return result

    def model_string(
        self, mode: Literal["str", "list"] = "string", **kwargs
    ) -> str | list[str]:
        """将字典中的格式化字符串连接成一个字符串。
        
        
                !!! 注意：
                    By alias 为 True，因为在某些情况下，特别是 CropSettings，数据库中参数的 WOFOST 名���与 SWAP 中使用的名称不同。
                    这允许这些参数正确匹配，但在 SWAP 输入文件中正确序列化。
        
                参数：
                    mode (Literal["str", "list]): 输出格式。
                    kwargs (dict): 传递给 `model_dump()` 的附加关键字参数。
        """
        dump = self.model_dump(
            mode="json", exclude_none=True, by_alias=True, **kwargs
        ).values()

        if mode == "list":
            return list(dump)
        else:
            return "\n".join(dump)


class YAMLValidatorMixin(BaseModel):
    """一个混入类，为模型提供基于 YAML 的验证。
    
        最初，pySWAP 在每个模型组件类上都有模型序列化器，其中包含许多断言来验证参数（即，如果 swrd = 3，则需要参数 rlwtb 和 wrtmax）。
        这在代码中造成了混乱，并且由于检查工具无论如何都没有使用它，因此决定将验证逻辑保留在代码中，并将规则移动到单独的 YAML 文件中。
    
        方法：
            validate_parameters: 根据所需规则验证参数。
            validate_with_yaml: 执行验证逻辑的 Pydantic 验证器。
    """

    _validation: bool = PrivateAttr(default=False)

    @staticmethod
    def validate_parameters(
        switch_name: str, switch_value: str, params: dict, rules: dict
    ):
        """根据所需规则验证参数。
        
                此方法从 YAML 文件读取模型的规则，并检查是否存在所需的参数。如果不存在，则引发 ValueError。
        
                ```yaml
                SaltStress: # <--- 模型名称
                    swsalinity:  # <--- 开关名称 (switch_name)
                        1:  # <--- 开关值 (switch_value)
                        - saltmax  # <---| 所需参数
                        - saltslope  # <--|
                        2:
                        - salthead
                ```
        
                参数：
                    switch_name (str): 开关的名称（例如，'swcf'）。
                    switch_value (Any): 开关的值（例如，1 或 2）。
                    params (dict): 要检查的参数字典。
                    rules (dict): 包含验证规则的字典。
        
                引发：
                    ValueError: 如果缺少所需参数。
        """

        required_params = rules.get(switch_name, {}).get(switch_value, [])

        if not required_params:
            return  # 此开关值没有规则

        missing_params = [
            param for param in required_params if params.get(param) is None
        ]

        if missing_params:
            msg = f"The following parameters are required for {switch_name}={switch_value}: {', '.join(missing_params)}"
            raise ValueError(msg)

    @model_validator(mode="after")
    def validate_with_yaml(self) -> Self:
        """执行验证逻辑的 Pydantic 验证器。
        
                模型上定义的所有验证器都在模型实例化时运行。此方法确保 YAML 验证被推迟，直到
                _validation 参数（所有继承此混入的类都必需）设置为 True。当所有必需参数都
                被假定已设置时，例如当用户尝试运行模型时，此状态完成。
        """

        if not self._validation:
            return self

        rules = VALIDATIONRULES.get(self.__class__.__name__, {})

        for switch_name in rules:
            switch_value = getattr(self, switch_name, None)
            if switch_value is not None:  # 仅当开关设置时才验证
                self.validate_parameters(
                    switch_name, switch_value, self.__dict__, rules
                )

        self._validation = False
        return self


class WOFOSTUpdateMixin:
    """pySWAP 的 WOFOST 作物参数数据库接口。
    
            此混入应由与 WOFOST 作物数据库共享参数的类继承。
    """

    def update_from_wofost(self) -> None:
        """使用 WOFOST 品种设置更新模型。"""
        from pyswap.utils.old_swap import create_array_objects

        # parameters 属性返回一个包含键值对和列表的字典。在更新之前，应该创建表格。
        if not hasattr(self, "wofost_variety"):
            msg = "模型没有 WOFOST 品种设置。"
            raise AttributeError(msg)

        variety_params = self.wofost_variety.parameters
        new_arrays = create_array_objects(variety_params)
        new = variety_params | new_arrays
        self.update(new, inplace=True)
