{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Hu<PERSON>lbrook 模型设置\n", "\n", "SWAP 纯文本配置文件（.swp、.crp、.dra、.bbc）由各种开关（以“SW”为前缀的参数）、键值对和表格组成。这些元素被分组到与模型的特定部分相对应的区域中。例如，气象部分包括气候计算的参数，而作物部分则处理与作物水分吸收等相关的参数。您可以在 [wiki 部分](/wiki/input-files/1-input-files/) 中查看 SWAP 文件输入的经典模板结构。\n", "\n", "pyswap 基于面向对象编程原则构建，类似于用于 MODFLOW 模型的 [flopy](https://flopy.readthedocs.io/en/stable/) 包。模型的不同部分或模型组件由单独的类（`GeneralSettings`、`Meteorology` 等）表示。然后，这些组件像乐高积木一样堆叠在一起，以构建一个完整的模型。这种方法允许通过修改或替换单个组件而不改变整体结构来轻松构建模型的变体。\n", "\n", "<div class=\"admonition tip\">\n", "  <p class=\"admonition-title\">提示</p>\n", "  <p style=\"margin-top: 10px;\" class=\"admonition-content\">    \n", "    pySWAP 在设计时考虑了类型提示。当使用像 Visual Studio Code 这样的代码编辑器时，输入 `pyswap.` 将显示可用类和函数的提示。同样，将鼠标悬停在类签名上将显示可用的参数、取值范围和文档。\n", "  </p>\n", "</div>"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from datetime import datetime as dt\n", "\n", "import pyswap as psp"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 常规设置\n", "\n", "在 pySWAP 中，每个不同的部分都表示为一个类，类似于运行 MODFLOW 模型的流行 Python 包 flopy。因此，在某种意义上，用 pySWAP 定义模型的感觉类似于在经典的 ASCII 模板中定义它，但它为您提供了更多处理模型的选项。最后，当定义了所有必要的对象后，您就可以创建一个模型实例。您还可以定义一个空的模型实例，并在创建后将每个部分添加到该实例中。让我们为模型设置 `Metadata` 和 `GeneralSettings`。我们将从一个空的模型实例开始。"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 从一个空的模型实例开始\n", "ml = psp.Model()\n", "\n", "\n", "meta = psp.components.Metadata(\n", "    author=\"<PERSON>\",\n", "    institution=\"University of Somewhere\",\n", "    email=\"<EMAIL>\",\n", "    project=\"pySWAP test - hupselbrook\",\n", "    swap_ver=\"4.2\",\n", ")\n", "\n", "simset = psp.components.simsettings.GeneralSettings(\n", "    tstart=\"2002-01-01\",\n", "    tend=\"2004-12-31\",\n", "    extensions=[\"vap\", \"blc\", \"sba\", \"inc\", \"csv\"],\n", "    nprintday=1,\n", "    swerror=1,\n", "    swscre=0,\n", "    swmonth=1,\n", "    swyrvar=0,\n", "    datefix=\"31 12\",\n", "    inlist_csv=[\n", "        \"rain\",\n", "        \"irrig\",\n", "        \"interc\",\n", "        \"runoff\",\n", "        \"drainage\",\n", "        \"dstor\",\n", "        \"epot\",\n", "        \"eact\",\n", "        \"tpot\",\n", "        \"tact\",\n", "        \"qbottom\",\n", "        \"gwl\",\n", "    ],\n", ")\n", "\n", "# 将模型组件附加到模型实例\n", "ml.metadata = meta\n", "ml.generalsettings = simset"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<div class=\"admonition note\">\n", "  <p class=\"admonition-title\">注意</p>\n", "  <p style=\"margin-top: 10px;\" class=\"admonition-content\">    \n", "    在这个阶段，有一个重要的区别需要解释；您不需要（实际上也不能）在开始时调整路径。这是因为 pySWAP 在一个临时目录中运行 SWAP 并自动处理路径。文件名也是如此；输入（例如，排水文件）的默认和冻结文件名为“swap”，输出的默认和冻结文件名为“result”。\n", "  </p>\n", "</div>\n", "\n", "添加这两个部分后，您可以查看该部分作为 SWAP 兼容字符串的外观，或者通过调用 `ml.swp` 属性查看 .swp 文件的当前形状。您可能会注意到比您想要设置的参数更多。这是因为默认情况下，热流 (`HeatFlow`)、溶质运移 (`SoluteTransport`) 和固定灌溉 (`FixedIrrigation`) 模块在模型中是关闭的。理查德方程 (`RichardsSettings`) 的某些参数也有默认设置。要更改它，只需定义您自己的具有所需设置的对象并在 `ml` 实例中重新分配它们就足够了。"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(ml.swp)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 气象\n", "\n", "设置气象部分还需要以特定的 CSV 格式提供一些气候数据。这些数据包含在 `File` 类型对象中。对于气象数据，它是 `MetFile` 类。让我们创建一个带有 `MetFile` 的 `Meteorology` 对象。\n", "\n", "目前，获取 MetFile 的其他内置方法是使用 KNMI 服务或从 CSV 文件加载。请阅读 `pyswap.components.meteorology` 模块的文档。"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 这里我们还需要从 testcase 库加载气象数据\n", "from pyswap import testcase\n", "\n", "meteo_data = psp.components.meteorology.metfile_from_csv(\n", "    metfil=\"283.met\", csv_path=testcase.get_path(\"hupselbrook\", \"met\")\n", ")\n", "\n", "meteo = psp.components.meteorology.Meteorology(\n", "    lat=52.0,\n", "    alt=21.0,\n", "    swetr=0,\n", "    metfile=meteo_data,\n", "    swdivide=1,\n", "    swmetdetail=0,\n", "    altw=10.0,\n", "    angstroma=0.25,\n", "    angstromb=0.5,\n", ")\n", "\n", "ml.meteorology = meteo"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 作物\n", "\n", "作物设置既可以完全在您的 Python 脚本中定义，也可以部分从 WOFOST 作物数据库导入（取决于您要使用的作物文件类型）。在 Hupselbrook 示例中，作物轮作设置包括所有三种：玉米的简单设置、马铃薯的详细 WOFOST 模型和动态草地生长。\n", "\n", "每个作物文件都设置为 `Crop` 对象，而 `Crop` 对象又由子部分（`ScheduledIrrigation`、`CropDevelopmentSettingsFixed` 等）组成。这与主 `Model` 的构成方式相同。\n", "\n", "这部分有点长，所以请坐稳了..."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 玉米的简单（固定）作物设置"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["maize_prep = psp.components.crop.Preparation(\n", "    swprep=0, swsow=0, swgerm=0, dvsend=3.0, swharv=0\n", ")\n", "\n", "scheduled_irrigation = psp.components.irrigation.ScheduledIrrigation(schedule=0)\n", "\n", "DVS = [0.0, 0.3, 0.5, 0.7, 1.0, 1.4, 2.0]\n", "\n", "# 这是在 pySWAP 中创建和验证表格的一种方法。\n", "maize_gctb = psp.components.crop.GCTB.create({\n", "    \"DVS\": DVS,\n", "    \"LAI\": [0.05, 0.14, 0.61, 4.10, 5.00, 5.80, 5.20],\n", "})\n", "\n", "maize_cftb = psp.components.crop.CFTB.create({\n", "    \"DVS\": DVS,\n", "    \"CH\": [1.0, 15.0, 40.0, 140.0, 170.0, 180.0, 175.0],\n", "})\n", "\n", "maize_rdtb = psp.components.crop.RDTB.create({\n", "    \"DVS\": [0.0, 0.3, 0.5, 0.7, 1.0, 2.0],\n", "    \"RD\": [5.0, 20.0, 50.0, 80.0, 90.0, 100.0],\n", "})\n", "\n", "maize_rdctb = psp.components.crop.RDCTB.create({\n", "    \"RRD\": [0.0, 1.0],\n", "    \"RDENS\": [1.0, 0.0],\n", "})\n", "\n", "maize_cropdev_settings = psp.components.crop.CropDevelopmentSettingsFixed(\n", "    idev=1,\n", "    lcc=168,\n", "    kdif=0.6,\n", "    kdir=0.75,\n", "    swgc=1,\n", "    gctb=maize_gctb,\n", "    swcf=2,\n", "    cftb=maize_cftb,\n", "    albedo=0.23,\n", "    rsc=61.0,\n", "    rsw=0.0,\n", "    swrd=1,\n", "    rdtb=maize_rdtb,\n", "    rdctb=maize_rdctb,\n", ")\n", "\n", "maize_ox_stress = psp.components.crop.OxygenStress(\n", "    swoxygen=1,\n", "    swwrtnonox=0,\n", "    aeratecrit=0.5,\n", "    hlim1=-15.0,\n", "    hlim2u=-30.0,\n", "    hlim2l=-30.0,\n", ")\n", "\n", "maize_dr_stress = psp.components.crop.DroughtStress(\n", "    swdrought=1,\n", "    hlim3h=-325.0,\n", "    hlim3l=-600.0,\n", "    hlim4=-8000.0,\n", "    adcrh=0.5,\n", "    adcrl=0.1,\n", ")\n", "\n", "# 与固定作物设置共享\n", "\n", "maize_interception = psp.components.crop.Interception(swinter=1, cofab=0.25)\n", "\n", "crpmaize = psp.components.crop.CropFile(\n", "    name=\"maizes\",\n", "    prep=maize_prep,\n", "    scheduledirrigation=scheduled_irrigation,\n", "    cropdev_settings=maize_cropdev_settings,\n", "    oxygenstress=maize_ox_stress,\n", "    droughtstress=maize_dr_stress,\n", "    interception=maize_interception,\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 马铃薯的 WOFOST 设置\n", "\n", "对于 WOFOST 模型，需要更多的参数。因此，我们可以利用现有数据库中作物的校准参数。pySWAP 使用 de Wit 的作物数据库自动加载一些可用参数。其余参数仍需用户提供。我们将在下面使用该数据库。"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from pyswap import db\n", "\n", "# 加载作物数据库\n", "db = db.WOFOSTCropDB()\n", "potato = db.load_crop_file(\"potato\")\n", "potato_params = potato.get_variety(\"Potato_701\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["potato_prep = psp.components.crop.Preparation(\n", "    swprep=0,\n", "    swsow=0,\n", "    swgerm=2,\n", "    tsumemeopt=170.0,\n", "    tbasem=3.0,\n", "    teffmx=18.0,\n", "    hdrygerm=-500.0,\n", "    hwetgerm=-100.0,\n", "    zgerm=-10.0,\n", "    agerm=203.0,\n", "    dvsend=2.0,\n", "    swharv=0,\n", ")\n", "\n", "potato_chtb = psp.components.crop.CFTB.create({\n", "    \"DVS\": [0.0, 1.0, 2.0],\n", "    \"CH\": [\n", "        1.0,\n", "        40.0,\n", "        50.0,\n", "    ],\n", "})\n", "\n", "potato_rdctb = psp.components.crop.RDCTB.create({\n", "    \"RRD\": [0.0, 1.0],\n", "    \"RDENS\": [1.0, 0.0],\n", "})\n", "\n", "\n", "potato_cropdev_settings = psp.components.crop.CropDevelopmentSettingsWOFOST(\n", "    wofost_variety=potato_params,\n", "    swcf=2,\n", "    cftb=potato_chtb,\n", "    albedo=0.19,\n", "    laiem=0.0589,\n", "    ssa=0.0,\n", "    kdif=1.0,\n", "    rsc=207.0,\n", "    rsw=0.0,\n", "    kdir=0.75,\n", "    eff=0.45,\n", "    swrd=2,\n", "    rdc=50.0,\n", "    swdmi2rd=1,\n", "    rdctb=potato_rdctb,\n", ")\n", "\n", "potato_cropdev_settings.update_from_wofost()\n", "\n", "potato_ox_stress = psp.components.crop.OxygenStress(\n", "    swoxygen=1,\n", "    swwrtnonox=1,\n", "    aeratecrit=0.5,\n", "    hlim1=-10.0,\n", "    hlim2u=-25.0,\n", "    hlim2l=-25.0,\n", "    swrootradius=2,\n", "    root_radiuso2=0.00015,\n", ")\n", "\n", "potato_dr_stress = psp.components.crop.DroughtStress(\n", "    swdrought=1,\n", "    hlim3h=-300.0,\n", "    hlim3l=-500.0,\n", "    hlim4=-10000.0,\n", "    adcrh=0.5,\n", "    adcrl=0.1,\n", ")\n", "\n", "crppotato = psp.components.crop.CropFile(\n", "    name=\"potatod\",\n", "    prep=potato_prep,\n", "    cropdev_settings=potato_cropdev_settings,\n", "    oxygenstress=potato_ox_stress,\n", "    droughtstress=potato_dr_stress,\n", "    # 与固定作物设置共享\n", "    interception=maize_interception,\n", "    scheduledirrigation=scheduled_irrigation,\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 动态草地模型\n", "\n", "这个模型需要许多在 pySWAP 使用的作物数据库中不可用的参数。因此，所有表格和参数都必须手动定义。"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["grass_chtb = psp.components.crop.CFTB.create({\n", "    \"DNR\": [0.0, 180.0, 366.0],\n", "    \"CH\": [12.0, 12.0, 12.0],\n", "})\n", "\n", "grass_slatb = psp.components.crop.SLATB.create({\n", "    \"DNR\": [1.00, 80.00, 300.00, 366.00],\n", "    \"SLA\": [0.0015, 0.0015, 0.0020, 0.0020],\n", "})\n", "\n", "amaxtb = psp.components.crop.AMAXTB.create({\n", "    \"DNR\": [1.00, 95.00, 200.00, 275.00, 366.00],\n", "    \"AMAX\": [40.00, 40.00, 35.00, 25.00, 25.00],\n", "})\n", "\n", "grass_tmpftb = psp.components.crop.TMPFTB.create({\n", "    \"TAVD\": [0.00, 5.00, 15.00, 25.00, 40.00],\n", "    \"TMPF\": [0.00, 0.70, 1.00, 1.00, 0.00],\n", "})\n", "grass_tmnftb = psp.components.crop.TMNFTB.create({\n", "    \"TMNR\": [0.0, 4.0],\n", "    \"TMNF\": [0.0, 1.0],\n", "})\n", "\n", "grass_rfsetb = psp.components.crop.RFSETB.create({\n", "    \"DNR\": [1.00, 366.00],\n", "    \"RFSE\": [1.0000, 1.0000],\n", "})\n", "\n", "grass_frtb = psp.components.crop.FRTB.create({\n", "    \"DNR\": [1.00, 366.00],\n", "    \"FR\": [0.3000, 0.3000],\n", "})\n", "\n", "grass_fltb = psp.components.crop.FLTB.create({\n", "    \"DNR\": [1.00, 366.00],\n", "    \"FL\": [0.6000, 0.6000],\n", "})\n", "\n", "grass_fstb = psp.components.crop.FSTB.create({\n", "    \"DNR\": [1.00, 366.00],\n", "    \"FS\": [0.4000, 0.4000],\n", "})\n", "\n", "grass_rdrrtb = psp.components.crop.RDRRTB.create({\n", "    \"DNR\": [1.0, 180.0, 366.0],\n", "    \"RDRR\": [0.0, 0.02, 0.02],\n", "})\n", "\n", "grass_rdrstb = psp.components.crop.RDRSTB.create({\n", "    \"DNR\": [1.0, 180.0, 366.0],\n", "    \"RDRS\": [0.0, 0.02, 0.02],\n", "})\n", "\n", "grass_rlwtb = psp.components.crop.RLWTB.create({\n", "    \"RW\": [300.00, 2500.00],\n", "    \"RL\": [20.0, 40.0],\n", "})\n", "\n", "grass_rdctb = psp.components.crop.RDCTB.create({\n", "    \"RRD\": [0.0, 1.0],\n", "    \"RDENS\": [1.0, 0.0],\n", "})\n", "\n", "grass_settings = psp.components.crop.CropDevelopmentSettingsGrass(\n", "    swcf=2,\n", "    cftb=grass_chtb,\n", "    albedo=0.23,\n", "    rsc=100.0,\n", "    rsw=0.0,\n", "    tdwi=1000.00,\n", "    laiem=0.63000,\n", "    rgrlai=0.00700,\n", "    swtsum=1,\n", "    ssa=0.0004,\n", "    span=30.00,\n", "    tbase=0.00,\n", "    slatb=grass_slatb,\n", "    kdif=0.60,\n", "    kdir=0.75,\n", "    eff=0.50,\n", "    amaxtb=amaxtb,\n", "    tmpftb=grass_tmpftb,\n", "    tmnftb=grass_tmnftb,\n", "    cvl=0.6850,\n", "    cvr=0.6940,\n", "    cvs=0.6620,\n", "    q10=2.0000,\n", "    rml=0.0300,\n", "    rmr=0.0150,\n", "    rms=0.0150,\n", "    rfsetb=grass_rfsetb,\n", "    frtb=grass_frtb,\n", "    fltb=grass_fltb,\n", "    fstb=grass_fstb,\n", "    perdl=0.050,\n", "    rdrrtb=grass_rdrrtb,\n", "    rdrstb=grass_rdrstb,\n", "    swrd=3,\n", "    swdmi2rd=1,\n", "    rlwtb=grass_rlwtb,\n", "    wrtmax=3000.0,\n", "    swrdc=0,\n", "    rdctb=grass_rdctb,\n", ")\n", "\n", "grass_ox_stress = psp.components.crop.OxygenStress(\n", "    swoxygen=1, hlim1=0.0, hlim2u=1.0, hlim2l=-1.0, swwrtnonox=0\n", ")\n", "\n", "grass_drought_stress = psp.components.crop.DroughtStress(\n", "    swdrought=1,\n", "    swjarvis=4,\n", "    alphcrit=0.7,\n", "    hlim3h=-200.0,\n", "    hlim3l=-800.0,\n", "    hlim4=-8000.0,\n", "    adcrh=0.5,\n", "    adcrl=0.1,\n", ")\n", "\n", "grass_salt_stress = psp.components.crop.SaltStress(swsalinity=0)\n", "\n", "grass_interception = psp.components.crop.Interception(swinter=1, cofab=0.25)\n", "\n", "grass_co2 = psp.components.crop.CO2Correction(swco2=0)\n", "\n", "grass_dmmowtb = psp.components.crop.DMMOWTB.create({\n", "    \"DNR\": [120.0, 152.0, 182.0, 213.0, 366.0],\n", "    \"DMMOW\": [4700.0, 3700.0, 3200.0, 2700.0, 2700.0],\n", "})\n", "\n", "grass_dmmowdelay = psp.components.crop.DMMOWDELAY.create({\n", "    \"DMMOWDELAY\": [0.0, 2000.0, 4000.0],\n", "    \"DAYDELAY\": [2, 3, 4],\n", "})\n", "\n", "grass_management = psp.components.crop.GrasslandManagement(\n", "    seqgrazmow=[2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2],\n", "    swharvest=1,\n", "    swdmmow=2,\n", "    dmmowtb=grass_dmmowtb,\n", "    maxdaymow=42,\n", "    swlossmow=0,\n", "    mowrest=700.0,\n", "    dmmowdelay=grass_dmmowdelay,\n", "    swpotrelmf=1,\n", "    relmf=0.90,\n", ")\n", "\n", "grass_irrigation = psp.components.irrigation.ScheduledIrrigation(schedule=0)\n", "\n", "crpgrass = psp.components.crop.CropFile(\n", "    name=\"grassd\",\n", "    cropdev_settings=grass_settings,\n", "    oxygenstress=grass_ox_stress,\n", "    droughtstress=grass_drought_stress,\n", "    saltstress=grass_salt_stress,\n", "    interception=grass_interception,\n", "    co2correction=grass_co2,\n", "    grasslandmanagement=grass_management,\n", "    scheduledirrigation=grass_irrigation,\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 主作物对象\n", "\n", "上面定义的三个 CropFile 对象将被 pySWAP 转换为 .crp 文件并在模拟中使用。它们需要被添加到定义 .swp 模型内部作物模拟基本设置的主 Crop 对象中。"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["croprotation = psp.components.crop.CROPROTATION.create({\n", "    \"CROPSTART\": [dt(2002, 5, 1), dt(2003, 5, 10), dt(2004, 1, 1)],\n", "    \"CROPEND\": [dt(2002, 10, 15), dt(2003, 9, 29), dt(2004, 12, 31)],\n", "    \"CROPFIL\": [\"'maizes'\", \"'potatod'\", \"'grassd'\"],\n", "    \"CROPTYPE\": [1, 2, 3],\n", "})\n", "\n", "crop = psp.components.crop.Crop(\n", "    swcrop=1,\n", "    rds=200.0,\n", "    croprotation=croprotation,\n", "    cropfiles={\"maizes\": crpmaize, \"potatod\": crppotato, \"grassd\": crpgrass},\n", ")\n", "\n", "ml.crop = crop"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 灌溉\n", "\n", "灌溉部分相对较短，如果使用固定灌溉应用，则需要一个灌溉事件的数据帧。"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["irrig_events = psp.components.irrigation.IRRIGEVENTS.create({\n", "    \"IRDATE\": [\"2002-01-05\"],\n", "    \"IRDEPTH\": [5.0],\n", "    \"IRCONC\": [1000.0],\n", "    \"IRTYPE\": [1],\n", "})\n", "\n", "fixed_irrigation = psp.components.irrigation.FixedIrrigation(\n", "    swirfix=1, swirgfil=0, irrigevents=irrig_events\n", ")\n", "\n", "ml.fixedirrigation = fixed_irrigation"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 土壤-水分参数部分\n", "\n", "本节定义土壤水分相互作用参数。"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 土壤水分"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["soilmoisture = psp.components.soilwater.SoilMoisture(swinco=2, gwli=-75.0)\n", "ml.soilmoisture = soilmoisture"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 地表径流"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["surfaceflow = psp.components.soilwater.SurfaceFlow(\n", "    swpondmx=0, pondmx=0.2, rsro=0.5, rsroexp=1.0, swrunon=0\n", ")\n", "\n", "ml.surfaceflow = surfaceflow"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 蒸发"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["evaporation = psp.components.soilwater.Evaporation(\n", "    cfevappond=1.25, swcfbs=0, rsoil=30.0, swredu=1, cofredbl=0.35, rsigni=0.5\n", ")\n", "\n", "ml.evaporation = evaporation"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 土壤剖面"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["soil_profile = psp.components.soilwater.SOILPROFILE.create({\n", "    \"ISUBLAY\": [1, 2, 3, 4],\n", "    \"ISOILLAY\": [1, 1, 2, 2],\n", "    \"HSUBLAY\": [10.0, 20.0, 30.0, 140.0],\n", "    \"HCOMP\": [1.0, 5.0, 5.0, 10.0],\n", "    \"NCOMP\": [10, 4, 6, 14],\n", "})\n", "\n", "soil_hydraulic_functions = psp.components.soilwater.SOILHYDRFUNC.create({\n", "    \"ORES\": [0.01, 0.02],\n", "    \"OSAT\": [0.42, 0.38],\n", "    \"ALFA\": [0.0276, 0.0213],\n", "    \"NPAR\": [1.491, 1.951],\n", "    \"KSATFIT\": [12.52, 12.68],\n", "    \"LEXP\": [-1.060, 0.168],\n", "    \"ALFAW\": [0.0542, 0.0426],\n", "    \"H_ENPR\": [0.0, 0.0],\n", "    \"KSATEXM\": [12.52, 12.68],\n", "    \"BDENS\": [1315.0, 1315.0],\n", "})\n", "\n", "soilprofile = psp.components.soilwater.SoilProfile(\n", "    swsophy=0,\n", "    soilprofile=soil_profile,\n", "    swhyst=0,\n", "    tau=0.2,\n", "    soilhydrfunc=soil_hydraulic_functions,\n", "    swmacro=0,\n", ")\n", "\n", "ml.soilprofile = soilprofile"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 排水"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dra = psp.components.drainage.DraFile(\n", "    dramet=2,\n", "    swdivd=1,\n", "    cofani=[1.0, 1.0],\n", "    swdislay=0,\n", "    lm2=11.0,\n", "    shape=0.8,\n", "    wetper=30.0,\n", "    zbotdr=-80.0,\n", "    entres=20.0,\n", "    ipos=2,\n", "    basegw=-200.0,\n", "    khtop=25.0,\n", ")\n", "\n", "drainage = psp.components.drainage.Drainage(swdra=1, drafile=dra)\n", "\n", "ml.lateraldrainage = drainage"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 底部边界条件"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["bottom_boundary = psp.components.boundary.BottomBoundary(swbbcfile=0, swbotb=6)\n", "\n", "ml.bottomboundary = bottom_boundary"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["result = ml.run()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(result.yearly_summary)"]}], "metadata": {"kernelspec": {"display_name": "pySWAP", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.12"}}, "nbformat": 4, "nbformat_minor": 2}