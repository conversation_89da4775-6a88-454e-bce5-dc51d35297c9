---
search:
  boost: 0.5
---

# .CRP

.crp 文件定义了不同作物模型的作物参数。SWAP 中有三种类型的作物模块：简单（固定）作物、WOFOST 实现和动态草地生长。每个模块都需要一组特定的变量，这些变量在其各自的 .crp 文件模板中列出。

- 简单（固定）作物 - 此模块使用一组固定的参数进行作物生长。
- WOFOST 作物 - 此模块遵循 WOFOST 模型，该模型更详细。
- 动态草地生长 - 此模块动态模拟草地随时间生长。

## 简单（固定）作物

??? filetemplate "CROPFIL_FIXED.template"

    ```txt
    {% include "./templates/CROPFIL_FIXED.template" %}
    ```

## WOFOST 作物

??? filetemplate "CROPFIL_WOFOST.template"

    ```txt
    {% include "./templates/CROPFIL_WOFOST.template" %}
    ```

## 草

即将添加。
