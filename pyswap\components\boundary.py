# mypy: disable-error-code="call-overload, misc, override"
# 豁免理由：
# - call-overload 和 misc 在我解包 Field() 调用中的值时引发（例如，**_UNITRANGE）。这种方法是正确的。
# - override 在 model_string 上引发，因为这些方法不共享相同的签名。这不是一个优先修复的问题。
"""边界条件设置。

类：

    BottomBoundary: 底部边界设置。
"""

from pathlib import Path as _Path
from typing import Literal as _Literal

from pydantic import (
    Field as _Field,
    PrivateAttr as _PrivateAttr,
)

from pyswap.core.basemodel import PySWAPBaseModel as _PySWAPBaseModel
from pyswap.core.fields import (
    Decimal2f as _Decimal2f,
    String as _String,
    Table as _Table,
)
from pyswap.core.valueranges import (
    UNITRANGE as _UNITRANGE,
    YEARRANGE as _YEARRANGE,
)
from pyswap.utils.mixins import (
    FileMixin as _FileMixin,
    SerializableMixin as _SerializableMixin,
    YAMLValidatorMixin as _YAMLValidatorMixin,
)


class BottomBoundary(
    _PySWAPBaseModel, _SerializableMixin, _YAMLValidatorMixin, _FileMixin
):
    """底部边界设置。

    在 SWAP 中，边界条件可以在 .swp 文件中指定，也可以在单独的 .bbc 文件中指定。
    `swbbcfile` 属性决定了边界条件是否写入 .bbc 文件。

    属性：
        swbbcfile (Optional[Literal[0, 1]]): 在当前文件 (0) 或单独的 .bbc 文件 (1) 中指定边界条件。
            首选在 .swp 文件中定义边界条件。bbcfil 将来可能会被弃用。

        swbotb (Optional[Literal[1, 2, 3, 4, 5, 6, 7, 8]]): 底部边界类型开关。

            * 1 - 规定地下水位；
            * 2 - 规定底部通量；
            * 3 - 根据深层含水层的压头计算底部通量；
            * 4 - 根据地下水位计算底部通量；
            * 5 - 规定底部隔室的土壤水压头；
            * 6 - 底部通量为零；
            * 7 - 土壤剖面自由排水；
            * 8 - 土壤-空气界面自由流出。

        sw2 (Optional[Literal[1, 2]]): 指定底部通量是使用正弦函数还是表格。

            * 1 - 正弦函数；
            * 2 - 表格。

        sw3 (Optional[Literal[1, 2]]): 指定深层含水层中的水头是使用正弦函数还是表格。

            * 1 - 正弦函数；
            * 2 - 表格。

        sw4 (Optional[Literal[0, 1]]): 可以指定一个额外的地下水通量，该通量添加到上述指定通量中。

            * 0 - 无额外通量；
            * 1 - 额外通量。

        swbotb3resvert (Optional[Literal[0, 1]]): 底部边界和地下水位之间垂直水力阻力的开关。

            * 0 - 包含垂直水力阻力
            * 1 - 抑制垂直水力阻力

        swbotb3impl (Optional[Literal[0, 1]]): 底部通量数值解的开关。

            * 0 - 显式解（当 SHAPE < 1.0 时始终选择）；
            * 1 - 隐式解。

        swqhbot (Optional[Literal[1, 2]]): 指定是使用指数关系还是表格。

            * 1 - 底部通量通过指数关系计算
            * 2 - 底部通量从表格中导出

        bbcfil (Optional[String]): 包含底部边界数据的文件名（不带 .BBC 扩展名）。
        sinave (Optional[Decimal2f]): 底部通量的平均值。
        sinamp (Optional[Decimal2f]): 底部通量正弦函数的振幅。
        sinmax (Optional[Decimal2f]): 底部通量最大值出现的时间。
        shape (Optional[Decimal2f]): 用于推导平均地下水位的形状因子。
        hdrain (Optional[Decimal2f]): 平均排水基准，用于校正平均地下水位。
        rimlay (Optional[Decimal2f]): 隔水层的垂直阻力。
        aqave (Optional[Decimal2f]): 下伏含水层的平均水头。
        aqamp (Optional[Decimal2f]): 水头正弦波的振幅。
        aqtmax (Optional[Decimal2f]): 水头最大值出现的第一个时间。
        aqper (Optional[Decimal2f]): 水头正弦波的周期。
        cofqha (Optional[Decimal2f]): 底部通量指数关系的系数 A。
        cofqhb (Optional[Decimal2f]): 底部通量指数关系的系数 B。
        cofqhc (Optional[Decimal2f]): 底部通量指数关系的系数 C。
        gwlevel (Optional[Table]): 包含地下水位数据的表格。
        qbot (Optional[Table]): 包含底部通量数据的表格。
        haquif (Optional[Table]): 包含下伏含水层平均压头数据的表格。
        qbot4 (Optional[Table]): 包含底部通量数据的表格。
        qtab (Optional[Table]): 包含地下水位-底部通量关系的表格。
        hbot5 (Optional[Table]): 包含底部隔室压头数据的表格。
    """

    _extension = _PrivateAttr(default="bbc")

    swbbcfile: _Literal[0, 1] | None = None
    bbcfil: _String | None = None
    swbotb: _Literal[1, 2, 3, 4, 5, 6, 7, 8] | None = None
    sw2: _Literal[1, 2] | None = None
    sw4: _Literal[0, 1] | None = None
    swbotb3resvert: _Literal[0, 1] | None = None
    swbotb3impl: _Literal[0, 1] | None = None
    swqhbot: _Literal[1, 2] | None = None
    sinave: _Decimal2f | None = _Field(ge=-10.0, le=10.0, default=None)
    sinamp: _Decimal2f | None = _Field(ge=-10.0, le=10.0, default=None)
    sinmax: _Decimal2f | None = _Field(**_YEARRANGE, default=None)
    shape: _Decimal2f | None = _Field(**_UNITRANGE, default=None)
    hdrain: _Decimal2f | None = _Field(ge=-10000.0, le=0.0, default=None)
    rimlay: _Decimal2f | None = _Field(ge=0, le=100000.0, default=None)
    aqave: _Decimal2f | None = _Field(ge=-10000, le=1000, default=None)
    aqamp: _Decimal2f | None = _Field(ge=0, le=1000.0, default=None)
    aqtmax: _Decimal2f | None = _Field(**_YEARRANGE, default=None)
    aqper: _Decimal2f | None = _Field(**_YEARRANGE, default=None)
    cofqha: _Decimal2f | None = _Field(ge=-100.0, le=100.0, default=None)
    cofqhb: _Decimal2f | None = _Field(ge=-1.0, le=1.0, default=None)
    cofqhc: _Decimal2f | None = _Field(ge=-10.0, le=10.0, default=None)
    gwlevel: _Table | None = None
    qbot: _Table | None = None
    haquif: _Table | None = None
    qbot4: _Table | None = None
    qtab: _Table | None = None
    hbot5: _Table | None = None

    def bbc(self) -> str:
        """返回表示 bbc 文件的字符串。"""
        return self._model_string(exclude={"swbbcfile", "bbcfil"})

    def _model_string(self, **kwargs) -> str:
        """处理模型字符串生成的内部方法。

        此方法旨在避免在从超类调用 model_string 方法时 pydantic 引发最大递归深度错误。
        """
        return super().model_string(**kwargs)

    def model_string(self, **kwargs) -> str:
        """覆盖 model_string 方法以处理 swbbcfile 属性。

        此方法在最终序列化步骤中调用，此时每个部分都转换为字符串表示形式。
        因此，根据 swbbcfile 属性，此函数将返回：

            - 完整的节字符串表示形式，例如当所有边界条件都包含在 .swp 文件中时，或者；
            - 它将只包含 swbbcfile 和 bbcfil（定义其他参数时文件的名称）。
                在这种情况下，其他参数将使用 write_bbc 方法写入单独的 .bbc 文件。
        """
        if self.swbbcfile == 1:
            return super().model_string(include={"swbbcfile", "bbcfil"}, **kwargs)
        else:
            return super().model_string()

    def write_bbc(self, path: _Path):
        """将底部边界条件写入 .bbc 文件。

        此方法仅在 swbbcfile 属性设置为 1 时可用。
        将整个节设置（除了在 .swp 文件中定义的 swbbcfile 和 bbcfil）写入单独的 .bbc 文件。

        参数：
            path (Path): .bbc 文件将保存到的目录路径。
        """
        if self.swbbcfile != 1:
            msg = "Bottom boundary conditions are not set to be written to a .bbc file."
            raise ValueError(msg)

        self.save_file(string=self.bbc(), fname=self.bbcfil, path=path)
