# mypy: disable-error-code="call-overload, misc"


from typing import Literal as _Literal

from pydantic import (
    Field as _Field,
    PrivateAttr as _PrivateAttr,
)

from pyswap.components.tables import SOILHYDRFUNC, SOILPROFILE
from pyswap.core.basemodel import PySWAPBaseModel as _PySWAPBaseModel
from pyswap.core.fields import (
    Decimal2f as _Decimal2f,
    Decimal3f as _Decimal3f,
    String as _String,
    Table as _Table,
)
from pyswap.core.valueranges import UNITRANGE as _UNITRANGE
from pyswap.utils.mixins import (
    SerializableMixin as _SerializableMixin,
    YAMLValidatorMixin as _YAMLValidatorMixin,
)

__all__ = [
    "Evaporation",
    "SnowAndFrost",
    "SoilMoisture",
    "SoilProfile",
    "SurfaceFlow",
    "SOILPROFILE",
    "SOILHYDRFUNC",
]


class Evaporation(_PySWAPBaseModel, _SerializableMixin, _YAMLValidatorMixin):
    """蒸发设置。

    属性：
        swcfbs (Literal[0, 1]): 使用土壤因子 CFBS 从 ETref 计算 Epot 的开关。
        swredu (Literal[0, 1, 2]): 潜在土壤蒸发量减少方法的开关：

            * 0 - 减少到最大达西通量。
            * 1 - 减少到最大达西通量和最大 Black (1969)。
            * 2 - 减少到最大达西通量和最大 Boesten/Stroosnijder (1986)。

        cfevappond (Optional[Decimal2f]): 当使用 ETref 时，积水情况下的蒸发系数 [0..3]。
        cfbs (Optional[Decimal2f]): 潜在土壤蒸发系数 [0.5..1.5]。
        rsoil (Optional[Decimal2f]): 湿土壤的土壤阻力 [0..1000.0]。
        cofredbl (Optional[Decimal2f]): Black 的土壤蒸发系数 [0..1]。
        rsigni (Optional[Decimal2f]): 重置 Black 方法的最小降雨量 [0..100]。
        cofredbo (Optional[Decimal2f]): Boesten/Stroosnijder 的土壤蒸发系数 [0..1]。
    """

    swcfbs: _Literal[0, 1] | None = None
    swredu: _Literal[0, 1, 2] | None = None
    cfevappond: _Decimal2f | None = _Field(default=None, ge=0, le=3)
    cfbs: _Decimal2f | None = _Field(default=None, ge=0.5, le=1.5)
    rsoil: _Decimal2f | None = _Field(default=None, ge=0, le=1000.0)
    cofredbl: _Decimal2f | None = _Field(default=None, **_UNITRANGE)
    rsigni: _Decimal2f | None = _Field(default=None, ge=0, le=100)
    cofredbo: _Decimal2f | None = _Field(default=None, **_UNITRANGE)


class SnowAndFrost(_PySWAPBaseModel, _SerializableMixin, _YAMLValidatorMixin):
    """模型的积雪和霜冻设置。

    属性：
        swsnow (Literal[0, 1]): 积雪累积和融化计算开关。
        swfrost (Literal[0, 1]): 霜冻情况下减少土壤水流的开关。
        snowinco (Optional[Decimal2f]): 初始雪水当量 [0..1000 cm]。
        teprrain (Optional[Decimal2f]): 所有降水均为雨的温度上限 [0..10 oC]。
        teprsnow (Optional[Decimal2f]): 所有降水均为雪的温度下限 [-10..0 oC]。
        tfroststa (Optional[Decimal2f]): 水通量开始减少的土壤温度 (oC) [-10.0..5.0 oC]。
        tfrostend (Optional[Decimal2f]): 水通量结束减少的土壤温度 (oC) [-10.0..5.0 oC]。
    """

    swsnow: _Literal[0, 1] | None = None
    swfrost: _Literal[0, 1] | None = None
    snowinco: _Decimal2f | None = _Field(default=None, ge=0, le=1000)
    teprrain: _Decimal2f | None = _Field(default=None, ge=0, le=10)
    teprsnow: _Decimal2f | None = _Field(default=None, ge=-10, le=0)
    tfroststa: _Decimal2f | None = _Field(default=None, ge=-10, le=5)
    tfrostend: _Decimal2f | None = _Field(default=None, ge=-10, le=5)


class SoilMoisture(_PySWAPBaseModel, _SerializableMixin, _YAMLValidatorMixin):
    """土壤含水量和水平衡。

    属性：
        swinco (Literal[1, 2, 3]): 初始土壤含水条件类型的开关：

            * 1 - 压头作为土壤深度的函数。
            * 2 - 每个隔室的压头与初始地下水位处于静水力平衡。
            * 3 - 从上一次 Swap 模拟的输出文件中读取最终压头。

        head_soildepth (Optional[Table]): 包含压头和土壤深度数据的表格。
        gwli (Optional[Decimal2f]): 初始地下水位 [-10000..100 cm]。
        inifil (Optional[str]): 包含初始值的输出文件 *.END 的名称。
    """

    swinco: _Literal[1, 2, 3] | None = None
    head_soildepth: _Table | None = None
    gwli: _Decimal2f | None = _Field(default=None, ge=-10000, le=100)
    inifil: _String | None = None


class SoilProfile(_PySWAPBaseModel, _SerializableMixin, _YAMLValidatorMixin):
    """土壤剖面的垂直离散化、土壤水力函数和土壤持水滞后现象。

    涵盖 .swp 文件的第 4、5、6 和 7 部分。

    属性：
        swsophy (Literal[0, 1]): 解析函数或表格输入的开关

            * 0 - 带有 Mualem - van Genuchten 参数输入的解析函数
            * 1 - 土壤物理表格

        swhyst (Literal[0, 1, 2]): 土壤持水函数的滞后现象

            * 0 - 无滞后
            * 1 - 滞后，初始条件湿润
            * 2 - 滞后，初始条件干燥

        filenamesophy (Optional[str]): 包含每个土壤层土壤水力表格的输入文件名称
        tau (Optional[Decimal2f]): 改变湿润-干燥的最小压头差 [0..1000]。
        swmacro (Literal[0, 1]): 大孔隙优先流的开关
        soilprofile (Table): 包含土壤剖面数据的表格
        soilhydrfunc (Optional[Table]): 包含土壤水力函数的表格
    """

    _validation: bool = _PrivateAttr(default=False)

    swsophy: _Literal[0, 1] | None = None
    swhyst: _Literal[0, 1, 2] | None = None
    swmacro: _Literal[0, 1] | None = None
    filenamesophy: _String | None = None
    tau: _Decimal2f | None = _Field(default=None, ge=0, le=1000)
    soilprofile: _Table | None = None
    soilhydrfunc: _Table | None = None


class SurfaceFlow(_PySWAPBaseModel, _SerializableMixin, _YAMLValidatorMixin):
    """地表径流设置（积水、径流和入渗）。

    属性：
        swpondmx (Literal[0, 1]): 径流积水阈值变化的开关

            * 0 - 径流积水阈值恒定
            * 1 - 径流积水阈值随时间变化

        swrunon (Literal[0, 1]): 入渗开关

            * 0 - 无入渗
            * 1 - 使用入渗数据

        rsro (Optional[Decimal2f]): 地表径流的排水阻力 [0.001..1.0]。
        rsroexp (Optional[Decimal2f]): 地表径流排水方程的指数 [0.01..10.0]。
        pondmx (Optional[Decimal2f]): 积水情况下，径流的最小厚度 [0..1000]。
        rufil (Optional[str]): 入渗文件名。
        pondmxtb (Optional[Table]): 径流最小厚度随时间变化的表格。
    """

    swpondmx: _Literal[0, 1] | None = None
    swrunon: _Literal[0, 1] | None = None
    rsro: _Decimal3f | None = _Field(default=None, ge=0.001, le=1.0)
    rsroexp: _Decimal2f | None = _Field(default=None, ge=0.01, le=10.0)
    pondmx: _Decimal2f | None = _Field(default=None, ge=0, le=1000)
    rufil: _String | None = None
    pondmxtb: _Table | None = None
