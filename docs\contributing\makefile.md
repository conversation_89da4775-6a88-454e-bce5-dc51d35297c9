# Makefile

当某个工作流（例如测试代码或文档）需要重复执行时，Makefile 特别有用。它允许您定义一组可以使用简单命令运行的任务，例如 `make <some-command-name>`。在此项目中，Makefile 提供了几个有用的选项：

- **test-hupsel**: 安装 poetry 环境和 pre-commit 钩子，然后运行特定的测试用例。
- **testcheck**: 在测试配置中运行代码质量工具，包括检查 Poetry 锁定文件、代码 linting、执行静态类型检查以及检查过时的依赖项。将 QC 工具的结果保存到日志文件中。
- **check**: 类似于 `testcheck`，但运行工具时不将输出保存到日志文件。
- **test**: 使用 pytest 运行测试套件，包括覆盖率报告。
- **build**: 清理构建工件并使用 Poetry 创建 wheel 文件。
- **clean-build**: 删除 `dist` 目录以清理构建工件。
- **publish**: 发布版本到 PyPI，首先进行试运行以确保一切设置正确。
- **build-and-publish**: 将构建和发布步骤合并为一个命令。
- **docs-test**: 测试文档是否可以在没有警告或错误的情况下构建。
- **docs**: 本地构建和提供文档。
- **help**: 列出所有可用的 make 命令及其描述。

这些命令也用于简化 GitHub 中的 CI 工作流。欢迎您在为项目贡献时定义更多命令。
