"""pySWAP 序列化和反序列化的自定义字段类型。

结合 Pydantic 使用自定义 Annotated 字段，可以将对象序列化和反序列化为 SWAP 中使用的适当格式。
当需要实现新类型的字段时，这种方法特别有用，设计应遵循以下模式：

```Python
CustonType = Annotated[
    <原生 Python 类型>,
    pydantic.AfterValidator(custom_parsing_function),
    pydantic.PlainSerializer(custom_serializer, return_type=<原生 Python 类型>, when_used="json"),
    pydantic.Field(<自定义字段参数>)
    ]
```

其中：

- <原生 Python 类型>: 用户必须提供的字段的原生 Python 类型（例如，int）。
- pydantic.AfterValidator (可选):
    Pydantic 验证器在用户传递的字段值最初解析和验证（例如，对于必需字段）后运行。
    然后它将自定义解析函数作为参数，并返回与 pySWAP 序列化兼容的最终值。
    有关自定义验证函数，请参阅 `pyswap.core.parsers` 模块。
- pydantic.PlainSerializer:
    当调用 Model.model_dump(mode="json") 时，会调用序列化器。
    在 pySWAP 内部，这在 PySWAPBaseModel.model_string() 方法中完成。
    它将自定义序列化函数作为参数，并以与 SWAP 兼容的字符串格式返回参数。
    有关自定义序列化函数，请参阅 `pyswap.core.serializers` 模块。
- pydantic.Field:
    在 Pydantic 中，Field 对象用于定义字段的元数据。在 pySWAP 中，
    context 属性用于将附加信息传递给序列化函数。

然后在模型中使用自定义类型：

```Python
class SWAPSection(PySWAPBaseModel):
    field: CustomType
```

此模块中的字段：

    Table (DataFrame): 一个 DataFrame 对象，序列化为仅包含标题和数据的字符串。
    Arrays (DataFrame): 一个 DataFrame 对象，序列化为仅包含数据列（无标题）的字符串，
        但前面带有变量名（例如，FLUXTB = 0.0 0.0/n 1.0 1.0）
    CSVTable (DataFrame): 一个 DataFrame 对象，序列化为 CSV 格式的包含标题和数据的字符串，
        专门为 .met 文件格式定制。
    DayMonth (d): 一个日期对象，序列化为仅包含日期和月份的字符串（例如，'01 01'）。
    StringList (List[str]): 一个字符串列表，序列化为以逗号分隔并用引号括起来的字符串
        （例如，'string1, string2, string3'）。
    FloatList (List[float]): 一个浮点数列表，序列化为以空格分隔的字符串。
    DateList (List[d]): 一个日期对象列表，序列化为以换行符分隔的字符串。
    Switch (bool | int): 一个布尔值或整数，序列化为整数（0 或 1）。
    ObjectList (list): 一个对象列表，序列化为以换行符分隔的字符串。
"""

from datetime import date
from decimal import Decimal
from typing import Annotated, TypeVar

from pandas import DataFrame
from pydantic import AfterValidator, BeforeValidator, Field
from pydantic.functional_serializers import PlainSerializer

from pyswap.core.basemodel import PySWAPBaseModel
from pyswap.core.parsers import (
    parse_day_month,
    parse_decimal,
    parse_float_list,
    parse_int_list,
    parse_quoted_string,
    parse_string_list,
)
from pyswap.core.serializers import (
    serialize_arrays,
    serialize_csv_table,
    serialize_day_month,
    serialize_decimal,
    serialize_table,
)

__all__ = [
    "Table",
    "Arrays",
    "CSVTable",
    "DayMonth",
    "StringList",
    "FloatList",
    "IntList",
    "DateList",
    "String",
    "File",
    "Subsection",
    "Decimal2f",
    "Decimal3f",
    "Decimal4f",
]

Table = Annotated[
    DataFrame,
    PlainSerializer(serialize_table, return_type=str, when_used="json"),
    Field(json_schema_extra={"is_annotated_exception_type": True}),
]
"""将带有标题的 pd.DataFrame 序列化为不带前导变量名的字符串。"""

Arrays = Annotated[
    DataFrame, PlainSerializer(serialize_arrays, return_type=str, when_used="json")
]
"""将不带标题的 pd.DataFrame 序列化为带前导变量名的字符串。"""

CSVTable = Annotated[
    DataFrame,
    PlainSerializer(
        lambda x: serialize_csv_table(x), return_type=str, when_used="json"
    ),
]
"""将 pd.DataFrame 序列化为 CSV 格式的字符串。"""

DayMonth = Annotated[
    date | str,
    AfterValidator(parse_day_month),
    PlainSerializer(serialize_day_month, return_type=str, when_used="json"),
]
"""将日期对象序列化为仅包含日期和月份的字符串。"""

StringList = Annotated[
    list[str] | str,
    AfterValidator(parse_string_list),
    PlainSerializer(lambda x: f"'{','.join(x)}'", return_type=str, when_used="json"),
]
"""将字符串列表序列化为以逗号分隔的字符串。"""

FloatList = Annotated[
    list[float] | str,
    AfterValidator(parse_float_list),
    PlainSerializer(
        lambda x: " ".join([f"{Decimal(f):.2f}" for f in x]),
        return_type=str,
        when_used="json",
    ),
]
"""将浮点数列表序列化为以空格分隔的字符串。"""

IntList = Annotated[
    list[int] | str,
    AfterValidator(parse_int_list),
    PlainSerializer(
        lambda x: " ".join([str(f) for f in x]), return_type=str, when_used="json"
    ),
]
"""将整数列表序列化为以空格分隔的字符串。"""

DateList = Annotated[
    list[date],
    PlainSerializer(
        lambda x: "\n" + "\n".join([d.strftime("%Y-%m-%d") for d in x]),
        return_type=str,
        when_used="json",
    ),
]
"""将日期对象列表序列化为以换行符分隔的字符串。"""

String = Annotated[
    str,
    PlainSerializer(lambda x: f"'{x}'", return_type=str),
    AfterValidator(parse_quoted_string),
]
"""将字符串序列化为带引号的字符串。"""

File = Annotated[
    PySWAPBaseModel,
    PlainSerializer(lambda x: x.model_string(), return_type=str, when_used="json"),
    Field(json_schema_extra={"is_annotated_exception_type": True}),
]
"""将 PySWAPBaseModel 序列化为字符串。"""

SubsectionTypeVar = TypeVar("SubsectionTypeVar", bound=PySWAPBaseModel)

Subsection = Annotated[
    SubsectionTypeVar,
    PlainSerializer(lambda x: x.model_string(), return_type=str),
    Field(json_schema_extra={"is_annotated_exception_type": True}),
]
"""将嵌套的 PySWAPBaseModel 序列化为字符串。"""

Decimal2f = Annotated[
    float | str,
    BeforeValidator(parse_decimal),
    PlainSerializer(serialize_decimal(precision=2), return_type=str, when_used="json"),
]
"""将浮点数序列化为保留 2 位小数的字符串。"""

Decimal3f = Annotated[
    float,
    PlainSerializer(serialize_decimal(precision=3), return_type=str, when_used="json"),
]
"""将浮点数序列化为保留 3 位小数的字符串。"""

Decimal4f = Annotated[
    float,
    PlainSerializer(serialize_decimal(precision=4), return_type=str, when_used="json"),
]
"""将浮点数序列化为保留 4 位小数的字符串。"""
