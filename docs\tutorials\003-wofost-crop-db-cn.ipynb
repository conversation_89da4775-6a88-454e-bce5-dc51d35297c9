{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# WOFOST作物数据库\n", "\n", "有一个GitHub代码库，其中包含一系列作物品种的校准参数。例如，这些参数曾被用于SWAP模型为马铃薯品种提供的Hupselbrook测试案例中。由于这些参数是可重用的，因此利用该数据库可以更轻松地为pySWAP创建有效的作物文件。\n", "\n", "在本Jupyter Notebook中，我们将为Hupselbrook测试案例中使用的马铃薯品种创建一个.crp文件。"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pyswap as psp"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 探索数据库\n", "\n", "pySWAP包含一个围绕A. Wit作物数据库的简单包装器。我们可以使用内置类来获取可用数据的概览。然后我们可以检查数据库中有哪些可用的作物类型。"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cropdb = psp.db.WOFOSTCropDB()\n", "cropdb.croptypes"]}, {"cell_type": "markdown", "metadata": {}, "source": ["当我们使用特定作物类型的名称调用数据库对象上的`load_crop_file`时，我们将得到一个WOFOSTCropFile对象。然后我们可以检查该对象，以查看文件的元数据、各个部分中的参数、可用的生态型和品种。"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cropfile = cropdb.load_crop_file(\"rice\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cropfile.metadata"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(cropfile.ecotypes)\n", "print(cropfile.varieties)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["rice_501 = cropfile.get_variety(\"Rice_501\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["新对象是`CropVariety`对象。在这里，您可以查看特定品种的元数据，或者仅以字典形式获取参数。请注意，在此阶段，所有表格都只是数字数组，其格式与.yaml文件中的格式相同。当它们用于填充作物设置类的参数时，pySWAP会自动将它们转换为表格。"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 使用CropVariety对象填充作物设置类\n", "\n", "数据库集成的意义在于能够直接从文件中填充swap使用的一些wofost作物参数。我们仍然需要手动设置许多参数，即准备、计划灌溉、截留、氧气胁迫、干旱胁迫等。"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["potato_prep = psp.components.crop.Preparation(\n", "    swprep=0,\n", "    swsow=0,\n", "    swgerm=2,\n", "    tsumemeopt=170.0,\n", "    tbasem=3.0,\n", "    teffmx=18.0,\n", "    hdrygerm=-500.0,\n", "    hwetgerm=-100.0,\n", "    zgerm=-10.0,\n", "    agerm=203.0,\n", "    dvsend=2.0,\n", "    swharv=0,\n", ")\n", "\n", "scheduled_irrigation = psp.components.irrigation.ScheduledIrrigation(schedule=0)\n", "interception = psp.components.crop.Interception(swinter=1, cofab=0.25)\n", "\n", "potato_chtb = psp.components.crop.CHTB.create({\n", "    \"DVS\": [0.0, 1.0, 2.0],\n", "    \"CH\": [\n", "        1.0,\n", "        40.0,\n", "        50.0,\n", "    ],\n", "})\n", "\n", "potato_rdctb = psp.components.crop.RDCTB.create({\n", "    \"RRD\": [0.0, 1.0],\n", "    \"RDENS\": [1.0, 0.0],\n", "})\n", "\n", "potato_ox_stress = psp.components.crop.OxygenStress(\n", "    swoxygen=1,\n", "    swwrtnonox=1,\n", "    aeratecrit=0.5,\n", "    hlim1=-10.0,\n", "    hlim2u=-25.0,\n", "    hlim2l=-25.0,\n", "    swrootradius=2,\n", "    root_radiuso2=0.00015,\n", ")\n", "\n", "potato_dr_stress = psp.components.crop.DroughtStress(\n", "    swdrought=1,\n", "    hlim3h=-300.0,\n", "    hlim3l=-500.0,\n", "    hlim4=-10000.0,\n", "    adcrh=0.5,\n", "    adcrl=0.1,\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["然后，我们可以从WOFOST数据库加载potato_701品种，并像这样创建CropDevelopmentSettings对象："]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["db = psp.db.WOFOSTCropDB()\n", "potato = db.load_crop_file(\"potato\")\n", "potato_params = potato.get_variety(\"Potato_701\")\n", "\n", "potato_cropdev_settings = psp.components.crop.CropDevelopmentSettingsWOFOST(\n", "    wofost_variety=potato_params,\n", "    swcf=2,\n", "    dvs_ch=potato_chtb,\n", "    albedo=0.19,\n", "    laiem=0.0589,\n", "    ssa=0.0,\n", "    kdif=1.0,\n", "    rsc=207.0,\n", "    rsw=0.0,\n", "    kdir=0.75,\n", "    eff=0.45,\n", "    swrd=2,\n", "    rdc=50.0,\n", "    swdmi2rd=1,\n", "    rdctb=potato_rdctb,\n", ")\n", "\n", "# 通过调用 `update_from_wofost` 方法，CropDevelopmentSettingsWOFOST 对象\n", "# 将会使用 WOFOST 作物文件中的值进行更新，并对模型进行评估，\n", "# 以确保没有缺少某些必需的设置。\n", "potato_cropdev_settings.update_from_wofost()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["接下来我们继续创建`CropFile`对象，该对象直接进入`Model`。您可以通过调用`CropFile.crp`属性来观察作物文件的内容。"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["crppotato = psp.components.crop.CropFile(\n", "    name=\"potatod\",\n", "    prep=potato_prep,\n", "    cropdev_settings=potato_cropdev_settings,\n", "    oxygenstress=potato_ox_stress,\n", "    droughtstress=potato_dr_stress,\n", "    interception=interception,\n", "    scheduledirrigation=scheduled_irrigation,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(crppotato.crp)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["WOFOST数据库集成允许您将数据库中已校准的设置引入pySWAP。如果您希望更改某些设置，您应该通过更新pySWAP的基础类（例如CropDevelopmentSettings）来完成，如下所示："]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["potato_cropdev_settings_tsum1_up = potato_cropdev_settings.update({\"tsum1\": 900})"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["crppotato_tsum1_up = crppotato.update({\n", "    \"cropdev_settings\": potato_cropdev_settings_tsum1_up\n", "})"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(crppotato_tsum1_up.cropdev_settings.tsum1)"]}], "metadata": {"kernelspec": {"display_name": "pyswap-U62T3krn-py3.11", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 2}