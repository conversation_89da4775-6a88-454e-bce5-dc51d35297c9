# mypy: disable-error-code="attr-defined"
# attr-defined 被禁用，因为在基类中实现一个 mixin 的部分功能更容易。
# 以后可以考虑修复
"""所有 pySWAP 模型继承的基模型

许多功能可以在基模型中抽象出来。这样，代码更 DRY (Don't Repeat Yourself)且更易于维护。
基模型用于强制输入数据的正确数据类型和结构。它们还提供
将数据转换为 SWAP 模型所需格式的方法。

此处定义的类基于 Pydantic BaseModel 和 Pandera DataFrameModel。
两者都旨在确保输入数据的正确数据类型和结构，
因为成功的验证意味着 SWAP 模型的顺利执行。
在 HPC 上作为提交作业运行时尤其重要。

类：
    BaseModel: pySWAP 模型的基类。继承自 Pydantic BaseModel。
    BaseTableModel: 验证 pandas DataFrame 的 pySWAP 模型的基类。继承自 Pandera DataFrameModel。
"""

from __future__ import annotations

from typing import Any

import pandas as pd
import pandera as pa
from pandera.typing import DataFrame
from pydantic import BaseModel, ConfigDict, field_validator

from pyswap.core.defaults import ADDITIONAL_SWITCHES


class PySWAPBaseModel(BaseModel):
    """pySWAP 模型的基类。

    方法：
        __setattr__: 覆盖方法以静默忽略冻结字段的赋值。
        update: 使用字典中的新值更新模型。
    """

    model_config = ConfigDict(
        arbitrary_types_allowed=True,
        validate_assignment=True,
        extra="ignore",
        populate_by_name=True,
    )

    def __setattr__(self, name, value):
        """静默忽略冻结字段的赋值。

        此方法被覆盖以静默忽略冻结字段的赋值，以避免读取旧 swp 文件时出错。
        """

        if name in self.model_fields and self.model_fields[name].frozen:
            return
        super().__setattr__(name, value)

    def update(self, new: dict, inplace: bool = False, no_validate: bool = False):
        """使用新值更新模型。

        给定值的字典首先被过滤，只包含模型中存在的字段。
        然后使用新值更新模型。返回更新后的模型（新模型或更新后的自身）。

        参数：
            new (dict): 包含新值的字典。
            inplace (bool): 如果为 True，则就地更新模型。
        """

        # filtered = {k: v for k, v in new.items() if k in self.model_fields}

        # updated_model = self.model_validate(dict(self) | filtered)
        updated_model = self.model_validate(dict(self) | new)

        if not inplace:
            # 添加此项是为了用户从经典 ASCII 文件加载模型的情况。
            # 此时使用 .update() 方法，但并非所有属性都会立即可用。
            # 在模型运行时仍将执行完整验证。
            if no_validate:
                updated_model._validation = False
            else:
                updated_model._validation = True
            updated_model.validate_with_yaml() if hasattr(
                updated_model, "validate_with_yaml"
            ) else None
            return updated_model

        else:
            for field, value in updated_model:
                setattr(self, field, value)
            if no_validate:
                updated_model._validation = False
            else:
                updated_model._validation = True
            self.validate_with_yaml() if hasattr(
                updated_model, "validate_with_yaml"
            ) else None

            return self

    @field_validator("*", mode="before")
    @classmethod
    def convert_switches(cls, value: Any, info: Any) -> Any:
        """将开关值转换为整数。

        此方法对于确保从 ASCII 文件加载模型能够正常工作是必要的。
        可以改进以包含不以“sw”开头的字面量。
        """
        if (
            (info.field_name.startswith("sw") or info.field_name in ADDITIONAL_SWITCHES)
            and info.field_name != "swap_ver"
            and value
        ):
            try:
                return int(value)
            except ValueError:
                return value
        return value


class BaseTableModel(pa.DataFrameModel):
    """pandas DataFrame 的基模型。

    方法：
        create: 从字典创建经过验证的 DataFrame。
    """

    class Config:
        coerce = True

    @classmethod
    def create(cls, data: dict, columns: list | None = None) -> DataFrame:
        df = pd.DataFrame(data)
        if columns:
            df.columns = columns
        else:
            df.columns = df.columns.str.upper()
        validated_df = cls.validate(df)
        return validated_df
