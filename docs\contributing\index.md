# 欢迎贡献者！

我们很高兴您能来到这里！本项目是开源的，完全免费使用。我们相信社区和协作的力量，并感谢您所做的任何贡献。无论是报告错误、建议新功能还是提交拉取请求，您的努力都有助于使该软件对每个人都更好。

## 报告错误

如果您发现错误，请通过在我们的 GitHub 存储库上打开一个问题来报告。提供尽可能多的详细信息，包括重现问题的步骤以及任何相关的日志或屏幕截图。为其添加相应的标签。

## 建议功能

我们欢迎对新功能的建议。请在我们的 GitHub 存储库上打开一个问题，并描述您希望看到的功能，包括任何潜在的用例。为其添加“enhancement”标签。

## 提交拉取请求

1. 打开一个问题以讨论您提议的更改。
2. Fork 存储库并从 `main` 创建您的分支。
3. 如果您添加了需要测试的代码，请添加测试。
4. 确保测试套件通过。
5. 确保您的代码符合 lint 规范。
6. 提交拉取请求，并附上对您更改的清晰描述和指向该问题的链接。

## 获取帮助

如果您需要帮助或有疑问，请随时通过打开问题或加入我们的社区讨论来联系我们。

感谢您的支持，祝您编码愉快！
