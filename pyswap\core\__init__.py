"""pySWAP 的核心功能。

核心包包含 SWAP 模型的主要类和函数。它主要由包内部使用。
没有直接向用户公开任何功能。

模块：
    basemodel: 所有 pySWAP 模型继承的基模型。
    defaults: 包中共享的变量。
    fields: pySWAP 序列化和反序列化的自定义字段类型。
    mixins: 增强特定 PySWAPBaseModel 功能的可重用混入。
    parsers: 将 SWAP 格式的 ascii 文件解析为 pySWAP 对象的函数。
    serializers: 微调 pySWAP 对象序列化为 SWAP 格式 ASCII 的函数。
    valueranges: pyswap 验证中使用的 pydantic Field 对象的取值范围。

子包：
    cli: pySWAP 的命令行界面（原型功能）。
    io: pySWAP 的输入/输出功能。
    db: pySWAP 的数据库集成。

资源：
    validation.yaml: 包含 pySWAP 模型验证模式的 YAML 文件
"""
