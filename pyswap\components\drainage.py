# mypy: disable-error-code="call-overload, misc"

"""侧向排水设置

.swp 文件的侧向排水设置，包括 .dra 文件设置。

类：
    Flux: .dra 文件中排水层之间的通量。
    DraFile: 排水文件 (.dra) 设置。
    Drainage: .swp 文件的侧向排水设置。
"""

from typing import Literal as _Literal

from pydantic import (
    Field as _Field,
    PrivateAttr as _PrivateAttr,
)

from pyswap.core.basemodel import PySWAPBaseModel as _PySWAPBaseModel
from pyswap.core.defaults import FNAME_IN as _FNAME_IN
from pyswap.core.fields import (
    File as _File,
    FloatList as _FloatList,
    String as _String,
    Subsection as _Subsection,
    Table as _Table,
)
from pyswap.core.valueranges import UNITRANGE as _UNITRANGE
from pyswap.utils.mixins import (
    FileMixin as _FileMixin,
    SerializableMixin as _SerializableMixin,
    YAMLValidatorMixin as _YAMLValidatorMixin,
)

__all__ = [
    "Flux",
    "DraFile",
    "Drainage",
]


class Flux(_PySWAPBaseModel, _SerializableMixin, _YAMLValidatorMixin):
    """ .dra 文件中排水层之间的通量。

    !!! 注意

        这被重写为单个类而不是类列表。
        简单性优于 DRY。无论如何，我猜首选的设置方式是通过扩展部分中的表格。

    属性：
        drares (float): 排水阻力 [10 .. 1e5 d]。
        infres (float): 渗透阻力 [10 .. 1e5 d]。
        swallo (Literal[1, 2]): 允许从该层排水的开关。

            * 1 - 允许排水和渗透。
            * 2 - 只允许渗透。
            * 3 - 只允许排水。
        
        l (Optional[float]): 排水间距 [1 .. 1e5 m]。
        zbotdr (float): 排水管底部的水位 [-1e4 .. 0 cm]。
        swdtyp (Literal[1, 2]): 排水类型。

            * 1 - 排水管。
            * 2 - 明渠。

        datowltb (Table): 日期 DATOWL [日期] 和河道水位 LEVEL。
            根据水位编号向数据框标题添加后缀。
    """

    drares1: float | None = _Field(default=None, ge=10.0, le=1.0e5)
    infres1: float | None = _Field(default=None, ge=10.0, le=1.0e5)
    swallo1: _Literal[1, 2, 3] | None = None
    l1: float | None = _Field(default=None, ge=1.0, le=1.0e5)
    zbotdr1: float = _Field(default=None, ge=-1000.0, le=0.0)
    swdtyp1: _Literal[1, 2] | None = None
    datowltb1: _Table | None = None
    # 级别 2
    drares2: float | None = _Field(default=None, ge=10.0, le=1.0e5)
    infres2: float | None = _Field(default=None, ge=10.0, le=1.0e5)
    swallo2: _Literal[1, 2, 3] | None = None
    l2: float | None = _Field(default=None, ge=1.0, le=1.0e5)
    zbotdr2: float | None = _Field(default=None, ge=-1000.0, le=0.0)
    swdtyp2: _Literal[1, 2] | None = None
    datowltb2: _Table | None = None
    # 级别 3
    drares3: float | None = _Field(default=None, ge=10.0, le=1.0e5)
    infres3: float | None = _Field(default=None, ge=10.0, le=1.0e5)
    swallo3: _Literal[1, 2, 3] | None = None
    l3: float | None = _Field(default=None, ge=1.0, le=1.0e5)
    zbotdr3: float | None = _Field(default=None, ge=-1000.0, le=0.0)
    swdtyp3: _Literal[1, 2] | None = None
    datowltb3: _Table | None = None
    # 级别 4
    drares4: float | None = _Field(default=None, ge=10.0, le=1.0e5)
    infres4: float | None = _Field(default=None, ge=10.0, le=1.0e5)
    swallo4: _Literal[1, 2, 3] | None = None
    l4: float | None = _Field(default=None, ge=1.0, le=1.0e5)
    zbotdr4: float | None = _Field(default=None, ge=-1000.0, le=0.0)
    swdtyp4: _Literal[1, 2] | None = None
    datowltb4: _Table | None = None
    # 级别 5
    drares5: float | None = _Field(default=None, ge=10.0, le=1.0e5)
    infres5: float | None = _Field(default=None, ge=10.0, le=1.0e5)
    swallo5: _Literal[1, 2, 3] | None = None
    l5: float | None = _Field(default=None, ge=1.0, le=1.0e5)
    zbotdr5: float | None = _Field(default=None, ge=-1000.0, le=0.0)
    swdtyp5: _Literal[1, 2] | None = None
    datowltb5: _Table | None = None


class DraFile(_PySWAPBaseModel, _FileMixin, _SerializableMixin):
    """排水文件 (.dra) 的内容。

    通用属性：
        dramet (Literal[1, 2, 3]): 侧向排水计算方法

            * 1 - 使用排水通量 - 地下水位关系表。
            * 2 - 使用 Hooghoudt 或 Ernst 的排水公式。
            * 3 - 使用排水/渗透阻力，如果需要可多级。

        swdivd (Literal[1, 2]): 计算地下水中排水通量的垂直分布。
        cofani (Optional[FloatList]): 为每个土壤层（最大 MAHO）指定各向异性因子 COFANI
            （水平/垂直饱和水力传导率）。
        swdislay (Literal[0, 1, 2, 3, '-']): 调整模型排水层上边界的开关。

            * 0 - 不调整
            * 1 - 根据模型排水层顶部的深度进行调整
            * 2 - 根据模型排水层顶部的因子进行调整
    
    排水通量表属性（选项 1）：
        lm1 (float): 排水间距
        table_qdrntb (Table): 排水通量 - 地下水位表。
        
    排水公式属性（选项 2）：
        lm2 (float): 排水间距。
        shape (float): 形状因子，用于考虑排水管和分水岭之间的实际位置。
        wetper (float): 排水管的湿周。
        zbotdr (float): 排水管底部的水位。
        entres (float): 排水管入口阻力。
        ipos (Literal[1, 2, 3, 4, 5]): 排水管位置

            * 1 - 在均质剖面中不透水层的顶部
            * 2 - 在均质剖面中不透水层的上方
            * 3 - 在细上层和粗下层土壤层的界面处
            * 4 - 在下部较粗的土壤层中
            * 5 - 在上部较细的土壤层中

        basegw (float): 不透水层的水位。
        khtop (float): 顶层的水平水力传导率。
        khbot (Optional[float]): 底层的水平水力传导率。
        zintf (Optional[float]): 粗细土壤层的界面水位。
        kvtop (Optional[float]): 顶层的垂直水力传导率。
        kvbot (Optional[float]): 底层的垂直水力传导率。
        geofac (Optional[float]): Ernst 的几何因子。

    排水渗透阻力属性（选项 3）：
        nrlevs (int): 排水层数。
        swintfl (Literal[0, 1]): 最高排水层（停留时间短的浅层系统）中层间流的选项。
        cofintflb (float): 层间流关系的系数。
        expintflb (float): 层间流关系的指数。
        swtopnrsrf (Literal[0, 1]): 启用模型排水层调整的开关。
        fluxes (Flux): 包含每个排水层参数的通量对象。
    
    扩展部分属性（地表水管理）：
        altcu (float): 控制单元相对于参考水位的高度。
        nrsrf (int): 地下排水层数。
        swnrsrf (Literal[0, 1, 2]): 引入快速地下排水的开关。
        rsurfdeep (Optional[float]): 快速地下排水的最大阻力。
        rsurfshallow (Optional[float]): 快速地下排水的最小阻力。
        swsrf (Literal[1, 2, 3]): 与地表水系统相互作用的开关。
        swsec (Optional[Literal[1, 2]]): 次级系统地表水位的选项。
        wlact (Optional[float]): 初始地表水位。
        osswlm (Optional[float]): 振荡警告的判据。
        nmper (Optional[int]): 管理期数。
        swqhr (Optional[Literal[1, 2]]): 排放关系类型的开关。
        sofcu (Optional[float]): 控制单元的大小。
    """

    _extension = _PrivateAttr("dra")
    # 通用
    dramet: _Literal[1, 2, 3] | None = None
    swdivd: _Literal[1, 2] | None = None
    cofani: _FloatList | None = None
    swdislay: _Literal[0, 1, 2, 3, "-"] | None = None
    # 排水通量表
    lm1: float | None = _Field(default=None, ge=1.0, le=1000.0)
    qdrntb: _Table | None = None
    # 排水公式
    lm2: float | None = _Field(default=None, ge=1.0, le=1000.0)
    shape: float | None = _Field(default=None, **_UNITRANGE)
    wetper: float | None = _Field(default=None, ge=0.0, le=1000.0)
    zbotdr: float | None = _Field(default=None, ge=-1000.0, le=0.0)
    entres: float | None = _Field(default=None, ge=0.0, le=1000.0)
    ipos: _Literal[1, 2, 3, 4, 5] | None = None
    basegw: float | None = _Field(default=None, ge=-1.0e4, le=0.0)
    khtop: float | None = _Field(default=None, ge=0.0, le=1000.0)
    khbot: float | None = _Field(default=None, ge=0.0, le=1000.0)
    zintf: float | None = _Field(default=None, ge=-1.0e4, le=0.0)
    kvtop: float | None = _Field(default=None, ge=0.0, le=1000.0)
    kvbot: float | None = _Field(default=None, ge=0.0, le=1000.0)
    geofac: float | None = _Field(default=None, ge=0.0, le=100.0)
    # 排水渗透阻力
    nrlevs: int | None = _Field(default=None, ge=1, le=5)
    swintfl: _Literal[0, 1] | None = None
    cofintflb: float | None = _Field(default=None, ge=0.01, le=10.0)
    expintflb: float | None = _Field(default=None, ge=0.1, le=1.0)
    swtopnrsrf: _Literal[0, 1] | None = None
    fluxes: _Subsection | None = None
    # 扩展部分
    altcu: float | None = _Field(default=None, ge=-300000.0, le=300000.0)
    drntb: _Table | None = None
    nrsrf: int | None = _Field(default=None, ge=1, le=5)
    swnrsrf: _Literal[0, 1, 2] | None = None
    rsurfdeep: float | None = _Field(default=None, ge=0.001, le=1000.0)
    rsurfshallow: float | None = _Field(default=None, ge=0.001, le=1000.0)
    cofintfl: float | None = _Field(default=None, ge=0.01, le=10.0)
    expintfl: float | None = _Field(default=None, ge=0.01, le=10.0)
    swsrf: _Literal[1, 2, 3] | None = None
    swsec: _Literal[1, 2] | None = None
    secwatlvl: _Table | None = None
    wlact: float | None = _Field(default=None, ge=-300000.0, le=300000.0)
    osswlm: float | None = _Field(default=None, ge=0.0, le=10.0)
    nmper: int | None = _Field(default=None, ge=1, le=3660)
    swqhr: _Literal[1, 2] | None = None
    sofcu: float | None = _Field(default=None, ge=0.1, le=100000.0)
    mansecwatlvl: _Table | None = None
    drainageleveltopparams: _Table | None = None
    qweir: _Table | None = None
    qweirtb: _Table | None = None
    priwatlvl: _Table | None = None

    @property
    def dra(self):
        return self.model_string()


class Drainage(_PySWAPBaseModel, _SerializableMixin, _YAMLValidatorMixin):
    """ .swp 文件中的侧向排水设置。

    属性：
        swdra (Literal[0, 1, 2]): 侧向排水开关。

            * 0 - 无排水。
            * 1 - 使用基本排水程序模拟。
            * 2 - 使用地表水管理模拟。

        drfil (str): 文件名。此属性是冻结的，无需更改。
        drafile (Optional[Any]): 排水文件的内容。
    """

    swdra: _Literal[0, 1, 2] | None = None
    drfil: _String | None = _Field(default=_FNAME_IN, frozen=True)
    drafile: _File | None = _Field(default=None, exclude=True)

    def write_dra(self, path: str) -> None:
        self.drafile.save_file(string=self.drafile.dra, fname=self.drfil, path=path)
        return None
