"""包中共享的变量。

变量：
    IS_WINDOWS: 系统是否为 Windows。
    BASE_P,TH: 文件路径的表示，取决于系统。
    root: 包的根目录。
    validation_rules: YAML 验证规则的路径。
    VALIDATIONRULES: 包的验证规则（字典形式）。
"""

import platform
from importlib import resources
from importlib.abc import Traversable

from pyswap.core.io.io_yaml import load_yaml

IS_WINDOWS = platform.system() == "Windows"
"""系统是否为 Windows。"""

BASE_PATH = ".\\" if IS_WINDOWS else "./"
"""文件路径的表示，取决于系统。"""

root: Traversable = resources.files("pyswap")
"""包的根目录。"""

validation_rules: Traversable = root / "core" / "validation.yaml"
VALIDATIONRULES = load_yaml(validation_rules)

FNAME_IN: str = "swap"
"""SWAP 输入文件的通用名称。"""

FNAME_OUT: str = "result"
"""SWAP 输出文件的通用名称。"""

ADDITIONAL_SWITCHES: list[str] = [
    "schedule",
    "isuas",
    "tcs",
    "tcsfix",
    "dcs",
    "dcslim",
    "dramet",
    "ipos",
    "idev",
    "idsl",
]

EXTENSIONS: list[str] = [
    "wba",
    "end",
    "vap",
    "bal",
    "blc",
    "sba",
    "ate",
    "bma",
    "drf",
    "swb",
    "ini",
    "inc",
    "crp",
    "str",
    "irg",
    "csv",
    "csv_tz",
]

EXTENSION_SWITCHES: list[str] = [f"sw{ext}" for ext in EXTENSIONS]
