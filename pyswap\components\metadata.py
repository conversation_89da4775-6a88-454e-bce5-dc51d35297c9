"""模型元数据。

类：
    Metadata: SWAP 模型的元数据。
"""

from pydantic import Field

from pyswap.core.basemodel import PySWAPBaseModel
from pyswap.core.fields import String
from pyswap.utils.mixins import SerializableMixin


class Metadata(PySWAPBaseModel, SerializableMixin):
    """SWAP 模型的元数据。

    元数据更多地是作为建模练习元数据而不是模型元数据。
    您应该在模型脚本的开头创建一个 Metadata 对象，并将其传递给您在其中创建的所有 Model 对象。
    它用于描述模型运行（如果它们存储在数据库中）。只有 `project` 会传递给交换文件。

    属性：
        author (str): 模型的作者。
        institution (str): 作者的机构。
        email (str): 作者的电子邮件。
        project (str): 项目名称。
        swap_ver (str): 使用的 SWAP 版本。
        comment (Optional[str]): 关于模型的注释。
    """

    author: String = Field(exclude=True)
    institution: String = Field(exclude=True)
    email: String = Field(exclude=True)
    project: String
    swap_ver: String = Field(exclude=True)
    comment: String | None = Field(default=None, exclude=True)
