"""用于 pyswap 验证的 pydantic Field 对象的取值范围。

Pydantic 的 Field() 对象用于定义模型字段的元数据和约束。
此模块包含 pySWAP 类中字段常用的值范围。

值范围：
    UNITRANGE (dict): 0.0 到 1.0 之间的值范围。
    YEARRANGE (dict): 年份的值范围 (0 <= x <= 366)。
    DVSRANGE (dict): 发育阶段的值范围 (0 <= x <= 2)。
"""

UNITRANGE = {"ge": 0.0, "le": 1.0}
"""0.0 到 1.0 之间的值范围。"""

YEARRANGE = {"ge": 0, "le": 366}
"""年份的值范围 (0 <= x <= 366)。"""

DVSRANGE = {"ge": 0.0, "le": 2.0}
"""发育阶段的值范围 (0 <= x <= 2)。"""
