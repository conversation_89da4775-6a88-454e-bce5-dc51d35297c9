**********************************************************************************
* Filename: swap.dra
* Contents: SWAP 4 - Input data for basic drainage and extended drainage
**********************************************************************************
* Comment area:
* Testbank of SWAP: build with template of dra-file
*
**********************************************************************************

*** BASIC DRAINAGE SECTION ***

**********************************************************************************
* Part 0: General

* Switch, method of lateral drainage calculation:
  DRAMET = 2                 ! 1 = Use table of drainage flux - groundwater level relation
                             ! 2 = Use drainage formula of Hooghoudt or Ernst
                             ! 3 = Use drainage/infiltration resistance, multi-level if needed

  SWDIVD = 1                 ! Calculate vertical distribution of drainage flux in groundwater [Y=1, N=0]

* If SWDIVD = 1, specify anisotropy factor COFANI (horizontal/vertical saturated hydraulic
* conductivity) for each soil layer (maximum MAHO), [0.0001..1000 -, R]:
  COFANI = 1.0 1.0

* Switch to adjust upper boundary of model discharge layer
  SWDISLAY = 0               ! 0 = No adjustment
                             ! 1 = Adjusment based on depth of top of model discharge
                             ! 2 = Adjusment based on factor of top of model discharge

* If SWDISLAY = 1 or 2, specify for the drainage systems (NRLEVS):
* SWTOPDISLAY = Switch for each drainage level to distribute drainage flux vertically with a given position of the top of the model discharge layers [Y=1, N=0]
* ZTOPDISLAY = Array with depth of top of model discharge layer for each drain level [-10000.0..0.0, cm, R]
* FTOPDISLAY = Array with factor of top of model discharge layer for each drain level [0.0..1.0, -, R]

 SWTOPDISLAY  ZTOPDISLAY  FTOPDISLAY
           1     -200.00         0.5
           0       -0.01         0.0
* End of table

**********************************************************************************

**********************************************************************************
* Part 1: Table of drainage flux - groundwater level relation (DRAMET = 1)

* If SWDIVD = 1, specify the drain spacing:
  LM1 = 30.0                 ! Drain spacing, [1..1000 m, R]

* Specify drainage flux QDRAIN [-100..1000 cm/d, R] as function of groundwater level GWL [-1000.0..10.0 cm, R]
* negative GWL is below soil surface; start with highest groundwater level; maximum of 25 records:

 QDRAIN     GWL
    0.5   -20.0
    0.1  -100.0
* End of table

**********************************************************************************

**********************************************************************************
* Part 2: Drainage formula of Hooghoudt or Ernst (DRAMET = 2)
*
* Drain characteristics:
  LM2 = 11.0                 ! Drain spacing, [1..1000 m, R]
  SHAPE = 0.8                ! Shape factor to account for actual location between drain and water divide [0.0..1.0 -, R]
  WETPER = 30.0              ! Wet perimeter of the drain, [0..1000 cm, R]
  ZBOTDR = -80.0             ! Level of drain bottom, [-1000..0 cm, R, neg. below soil surface]
  ENTRES = 20.0              ! Drain entry resistance, [0..1000 d, R]

* Soil profile characteristics:

* Position of drain:
  IPOS = 2                   ! 1 = On top of an impervious layer in a homogeneous profile
                             ! 2 = Above an impervious layer in a homogeneous profile
                             ! 3 = At the interface of a fine upper and a coarse lower soil layer
                             ! 4 = In the lower, more coarse soil layer
                             ! 5 = In the upper, more fine soil layer

* For all positions specify:
  BASEGW = -200.0            ! Level of impervious layer, [-1d4..0 cm, R]
  KHTOP = 25.0               ! Horizontal hydraulic conductivity top layer, [0..1000 cm/d, R]

* In addition, in case IPOS = 3, 4 or 5
  KHBOT = 10.0               ! horizontal hydraulic conductivity bottom layer, [0..1000 cm/d, R]
  ZINTF = -150.0             ! Level of interface of fine and coarse soil layer, [-1d4..0 cm, R]

* In addition, in case IPOS = 4 or 5
  KVTOP = 5.0                ! Vertical hydraulic conductivity top layer, [0..1000 cm/d, R]
  KVBOT = 10.0               ! Vertical hydraulic conductivity bottom layer, [0..1000 cm/d, R]

* In addition, in case IPOS = 5
  GEOFAC = 4.8               ! Geometry factor of Ernst,  [0..100 -, R]

**********************************************************************************

**********************************************************************************
* Part 3: Drainage and infiltration resistance (DRAMET = 3)

  NRLEVS = 2                 ! Number of drainage levels, [1..5, I]

* Option for interflow in highest drainage level (shallow system with short residence time)
  SWINTFL = 0                ! Switch for interflow [0,1, I]

* If SWINTFL = 1, specify:
  COFINTFLB = 0.5            ! Coefficient for interflow relation  [0.01..10.0 d, R]
  EXPINTFLB = 1.0            ! Exponent for interflow relation  [0.1..1.0 -, R]

* Switch to adjust the bottom of the model discharge layer in case of lateral interflow (SWDIVD=1).
* In case of SWTOPNRSRF = 1, the bottom of the highest order drainage system (ZBORDR(NUMDRAIN)) represents the maximum depth of the interflow.
 SWTOPNRSRF = 0              ! Switch to enable adjustment of model discharge layer [0,1, I]

**********************************************************************************

**********************************************************************************
* Part 3a: Drainage to level 1

  DRARES1 = 100.0            ! Drainage resistance, [10..1d5 d, R]
  INFRES1 = 100.0            ! Infiltration resistance, [0..1d5 d, R]

* Switch, for allowance drainage/infiltration:
  SWALLO1 = 1                ! 1 = Drainage and infiltration are both allowed
                             ! 2 = Drainage is not allowed
                             ! 3 = Infiltration is not allowed

* If SWDIVD = 1 (drainage flux vertically distributed), specify the drain spacing:
  L1 = 20.0                  ! Drain spacing, [1..100000 m, R]

  ZBOTDR1 = -90.0            ! Level of drainage medium bottom, [-10000..0 cm, R]
  SWDTYP1 = 2                ! Type of drainage medium: 1 = drain tube, 2 = open channel

* Specify date DATOWL1 [YYYY-MM-DD] and channel water level LEVEL1 [-10000..200, cm, R]
* LEVEL1 is negative if below soil surface; maximum MAOWL records:

    DATOWL1  LEVEL1
 2002-01-12   -90.0
 2002-12-14   -90.0
* End of table

**********************************************************************************

**********************************************************************************
* Part 3b: Drainage to level 2

  DRARES2 = 100.0            ! Drainage resistance, [10..1d5 d, R]
  INFRES2 = 100.0            ! Infiltration resistance, [0..1d5 d, R]

* Switch, for allowance drainage/infiltration:
  SWALLO2 = 1                ! 1 = Drainage and infiltration are both allowed
                             ! 2 = Drainage is not allowed
                             ! 3 = Infiltration is not allowed

* If SWDIVD = 1 (drainage flux vertically distributed), specify the drain spacing:
  L2 = 20.0                  ! Drain spacing, [1..100000 m, R]

  ZBOTDR2 = -90.0            ! Level of drainage medium bottom, [-10000..0 cm, R]
  SWDTYP2 = 2                ! Type of drainage medium: 1 = drain tube, 2 = open channel

* Specify date DATOWL2 [YYYY-MM-DD] and channel water level LEVEL2 [-10000..200, cm, R]
* LEVEL2 is negative if below soil surface; maximum MAOWL records:

    DATOWL2  LEVEL2
 2002-01-12   -90.0
 2002-12-14   -90.0
* End of table

**********************************************************************************

**********************************************************************************
* Part 3c: Drainage to level 3

  DRARES3 = 100.0            ! Drainage resistance, [10..1d5 d, R]
  INFRES3 = 100.0            ! Infiltration resistance, [0..1d5 d, R]

* Switch, for allowance drainage/infiltration:
  SWALLO3 = 1                ! 1 = Drainage and infiltration are both allowed
                             ! 2 = Drainage is not allowed
                             ! 3 = Infiltration is not allowed

* If SWDIVD = 1 (drainage flux vertically distributed), specify the drain spacing:
  L3 = 20.0                  ! Drain spacing, [1..100000 m, R]

  ZBOTDR3 = -90.0            ! Level of drainage medium bottom, [-10000..0 cm, R]
  SWDTYP3 = 2                ! Type of drainage medium: 1 = drain tube, 2 = open channel

* Specify date DATOWL3 [YYYY-MM-DD] and channel water level LEVEL3 [-10000..200, cm, R]
* LEVEL3 is negative if below soil surface; maximum MAOWL records:

    DATOWL3  LEVEL3
 2002-01-12   -90.0
 2002-12-14   -90.0
* End of table

**********************************************************************************

**********************************************************************************
* Part 3d: Drainage to level 4

  DRARES4 = 100.0            ! Drainage resistance, [10..1d5 d, R]
  INFRES4 = 100.0            ! Infiltration resistance, [0..1d5 d, R]

* Switch, for allowance drainage/infiltration:
  SWALLO4 = 1                ! 1 = Drainage and infiltration are both allowed
                             ! 2 = Drainage is not allowed
                             ! 3 = Infiltration is not allowed

* If SWDIVD = 1 (drainage flux vertically distributed), specify the drain spacing:
  L4 = 20.0                  ! Drain spacing, [1..100000 m, R]

  ZBOTDR4 = -90.0            ! Level of drainage medium bottom, [-10000..0 cm, R]
  SWDTYP4 = 2                ! Type of drainage medium: 1 = drain tube, 2 = open channel

* Specify date DATOWL4 [YYYY-MM-DD] and channel water level LEVEL4 [-10000..200, cm, R]
* LEVEL4 is negative if below soil surface; maximum MAOWL records:

    DATOWL4  LEVEL4
 2002-01-12   -90.0
 2002-12-14   -90.0
* End of table

**********************************************************************************

**********************************************************************************
* Part 3e: Drainage to level 5

  DRARES5 = 100.0            ! Drainage resistance, [10..1d5 d, R]
  INFRES5 = 100.0            ! Infiltration resistance, [0..1d5 d, R]

* Switch, for allowance drainage/infiltration:
  SWALLO5 = 1                ! 1 = Drainage and infiltration are both allowed
                             ! 2 = Drainage is not allowed
                             ! 3 = Infiltration is not allowed

* If SWDIVD = 1 (drainage flux vertically distributed), specify the drain spacing:
  L5 = 20.0                  ! Drain spacing, [1..100000 m, R]

  ZBOTDR5 = -90.0            ! Level of drainage medium bottom, [-10000..0 cm, R]
  SWDTYP5 = 2                ! Type of drainage medium: 1 = drain tube, 2 = open channel

* Specify date DATOWL5 [YYYY-MM-DD] and channel water level LEVEL5 [-10000..200, cm, R]
* LEVEL5 is negative if below soil surface; maximum MAOWL records:

    DATOWL5  LEVEL5
 2002-01-12   -90.0
 2002-12-14   -90.0
* End of table

**********************************************************************************

*** EXTENDED DRAINAGE SECTION ***

**********************************************************************************
* Part 0: Reference level

  ALTCU = 0.0                ! Altitude of the control unit relative to reference level [-300000..300000 cm, R]

**********************************************************************************

**********************************************************************************
* Part 1: drainage characteristics

  NRSRF = 2                  ! number of subsurface drainage levels [1..5, I]

* Table with physical characteristics of each subsurface drainage level:
* Variables RENTRY, REXIT, WIDTHR and TALUDR must have realistic values in case of open channels
* LEVEL = Drainage level number [1..NRSRF, I]
* SWDTYP = Type of drainage medium [open=0, closed=1]
* L = Spacing between channels/drains [1..100000 m, R]
* ZBOTDRE = Altitude of bottom of channel or drain [ALTCU-1000..ALTCU-0.01 cm,R]
* GWLINF = Groundwater level for maximum infiltration [-1000..0 cm, R]
* RDRAIN = Drainage resistance [1..100000 d, R]
* RINFI = Infiltration resistance  [1..100000 d, R]
* RENTRY = Entry resistance  [0..100 d, R]
* REXIT = Exit resistance   [0..100 d, R]
* WIDTHR = Bottom width of channel [0..10000 cm, R]
* TALUDR = Side-slope (dh/dw) of channel [0.01..5, R]

 LEV  SWDTYP      L  ZBOTDRE  GWLINF  RDRAIN   RINFI  RENTRY  REXIT  WIDTHR  TALUDR
   1       0  250.0   1093.0  -350.0   150.0  4000.0     0.8    0.8   100.0    0.66
   2       0  200.0   1150.0  -300.0   150.0  1500.0     0.8    0.8   100.0    0.66
* End of table

* Switch to introduce rapid subsurface drainage [0..2, I]
  SWNRSRF = 0                ! 0 = No rapid drainage
                             ! 1 = Rapid drainage in the highest drainage system (implies adjustment of RDRAIN of highest drainage system)
                             ! 2 = Rapid drainage as interflow according to a power relation (implies adjustment of RDRAIN of highest drainage system)

* In case of SWRNSRF = 1, specify rapid drainage
  RSURFDEEP = 30.0           ! Maximum resistance of rapid subsurface drainage [0.001..1000.0 d, R]
  RSURFSHALLOW = 10.0        ! Minimum resistance of rapid subsurface drainage [0.001..1000.0 d, R]

**********************************************************************************

**********************************************************************************
* Part 2: Specification and control of surface water system

* Switch for interaction with surface water system [1..3, I]
  SWSRF = 2                  ! 1 = No interaction with surface water system
                             ! 2 = Surface water system is simulated without separate primary system
                             ! 3 = Surface water system is simulated with separate primary system

* If SWSRF = 2 or 3, specify option for surface water level of secondary system [1..2, I]
  SWSEC = 2                  ! 1 = Surface water level is input
                             ! 2 = Surface water level is simulated


* Water level in secondary water course [ALTCU-1000..ALTCU-0.01 cm, R] as function of DATE2 [YYYY-MM-DD]
      DATE2     WLS
 2002-01-02  -100.0
 2002-06-14   -80.0
 2002-10-24  -120.0
* End of table

* Miscellaneous parameters
  WLACT = 1123.0             ! Initial surface water level [ALTCU-1000..ALTCU cm,R]
  OSSWLM = 2.5               ! Criterium for warning about oscillation [0..10 cm, R]

* Management of surface water levels
  NMPER = 4                  ! Number of management periods [1..3660, I]

* For each management period specify:
* IMPER = Index of management period [1..NMPER, I]
* IMPEND = Date that period ends [YYYY-MM-DD]
* SWMAN = Type of water management 1 = fixed weir crest, 2 = automatic weir [1..2, I]
* WSCAP = Surface water supply capacity [0..100 cm/d, R]
* WLDIP = Allowed dip of surface water level before starting supply [0..100 cm, R]
* INTWL = Length of water-level adjustment period (SWMAN = 2 only) [1..31 d, I]

 IMPER_4B      IMPEND  SWMAN  WSCAP  WLDIP  INTWL
        1  2002-01-31      1    0.0    0.0      1
        2  2002-04-01      2    0.0    5.0      1
        3  2002-11-01      2    0.0    5.0      1
        4  2002-12-31      1    0.0    0.0      1
* End of table

* Switch for type of discharge relationship [1..2, I]
  SWQHR = 1                  ! 1 = Exponential relationship
                             ! 2 = Table

* If SWQHR = 1, specify:
  SOFCU = 100.0              ! Size of the control unit [0.1..100000.0 ha, R]

* If SWQHR = 1, specify exponential discharge relation for all periods:
* IMPER = Index of management period [1..NMPER, I]
* HBWEIR = Weir crest; levels above soil surface are allowed, but simulated surface water levels should remain below 100 cm above soil surface;
*   the crest must be higher than the deepest channel bottom of the secondary system (ZBOTDR(1 or 2), [ALTCU-ZBOTDR..ALTCU+100 cm,R].
*   If SWMAN = 2, HBWEIR represents the lowest possible weir position.
* ALPHAW = Alpha-coefficient of discharge formula [0.1..50.0, R]
* BETAW = Beta-coefficient of discharge formula [0.5..3.0, R]

 IMPER_4C  HBWEIR  ALPHAW   BETAW
        1  1114.0     3.0  1.4765
        2  1110.0     3.0  1.4765
        3  1110.0     3.0  1.4765
        4  1114.0     3.0  1.4765
* End of table

* If SWQHR = 2, specify table discharge relation:
* IMPER = Index of management period [1..NMPER, I]
* IMPTAB = Index per management period [1..10, I]
* HTAB = Surface water level [ALTCU-1000..ALTCU+100 cm, R] (first value for each period = ALTCU + 100 cm)
* QTAB = Discharge [0..500 cm/d, R] (should go down to a value of zero at a level that is higher than the deepest channel bottom of secondary surface water system)

 IMPER_4D  IMPTAB    HTAB  QTAB
        1       1   100.0   2.0
        1       2     0.0   1.0
        1       3  -100.0   0.5
        1       4  -185.0   0.0
* End of table

* If SWSRF = 3, specify water levels in the primary system [max. = 52]
* No levels above soil surface for primary system

* Water level in primary water course [ALTCU-1000..ALTCU-0.01 cm, R] as function of DATE1 [YYYY-MM-DD]
      DATE1     WLP
 2002-01-02  -100.0
 2002-06-14   -80.0
 2002-10-24  -120.0
* End of table

**********************************************************************************

* End of .dra file           !
