# `pyswap` `PermissionError` 解决方案计划

## 问题描述

用户在运行 `pyswap` 模型时遇到 `PermissionError`，具体错误信息为：
`PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'd:\\code\\Python_item\\pySWAP-main\\docs\\tutorials\\tmp_9v14gc3\\283.met'`

此错误每次运行 Jupyter Notebook 单元格时都会出现，表明问题具有结构性。

## 诊断过程

1.  **审查 Jupyter Notebook (`docs/tutorials/000-setting-up-hupselbrook-cn.ipynb`)**：
    *   确认 `pyswap` 模型通过 `ml.run()` 调用。
    *   注意到 `pyswap` 在运行时会创建临时目录（例如 `tmp_9v14gc3`）来存储输入和输出文件。

2.  **检查 `pyswap/model/model.py`**：
    *   发现 `ModelRunner.run` 方法使用 `with tempfile.TemporaryDirectory(dir=path) as tempdir:` 来创建和管理临时目录。
    *   `tempfile.TemporaryDirectory` 在上下文管理器退出时会自动清理（删除）临时目录及其内容，而 `PermissionError` 正是在此时发生。
    *   `ModelBuilder` 负责将输入文件（包括 `met` 文件）复制到此临时目录。
    *   `ResultReader` 负责从临时目录读取结果文件。

3.  **检查 `pyswap/core/io/io_ascii.py`**：
    *   检查了 `open_ascii` 函数的实现，该函数用于读取 ASCII 文件。
    *   确认 `open_ascii` 使用 `with open(...) as f:` 语句，确保文件句柄在读取完成后会正确关闭。这排除了 `pyswap` 内部文件操作未关闭文件句柄导致文件锁定的可能性。

## 根本原因分析

由于 `open_ascii` 已正确关闭文件句柄，且错误每次都发生，最可能的根本原因在于：

*   **SWAP 可执行文件退出后文件句柄释放延迟**：在 Windows 操作系统上，即使子进程（SWAP 可执行文件）已经退出，操作系统也可能需要一小段时间来完全释放与该进程相关的文件句柄。如果 `tempfile.TemporaryDirectory` 的清理操作恰好发生在这个短暂的延迟期间，就会导致 `PermissionError`。

## 解决方案

为了解决 SWAP 可执行文件退出后文件句柄释放延迟导致的问题，我已在 `pyswap/model/model.py` 文件中添加了一个短暂的延迟。

**具体修改内容：**

*   **文件：** [`pyswap/model/model.py`](pyswap/model/model.py)
*   **修改类型：** `apply_diff`

```diff
<<<<<<< SEARCH
:start_line:27
-------
import logging
import os
import shutil
import subprocess
import tempfile
from multiprocessing import Pool
from pathlib import Path
from typing import Literal
=======
import logging
import os
import shutil
import subprocess
import tempfile
import time
from multiprocessing import Pool
from pathlib import Path
from typing import Literal
>>>>>>> REPLACE

<<<<<<< SEARCH
:start_line:230
-------
            ascii_files = reader.read_ascii_output()

            result.output.update(ascii_files)
            return result
=======
            ascii_files = reader.read_ascii_output()

            result.output.update(ascii_files)
            time.sleep(0.1) # 添加短暂延迟以确保文件句柄释放
            return result
>>>>>>> REPLACE
```

## 计划图示

```mermaid
graph TD
    A[用户报告 PermissionError] --> B{错误每次都发生吗？};
    B -- 是 --> C[检查 pyswap 内部文件处理代码];
    C -- 检查 ModelRunner.run --> D[发现使用 TemporaryDirectory];
    D -- 检查 open_ascii --> E[确认文件句柄已正确关闭];
    E --> F[推断问题可能在于 SWAP 可执行文件退出后文件句柄释放延迟];
    F --> G[在 ModelRunner.run 中添加短暂延迟];
    G --> H[用户确认计划并切换模式];