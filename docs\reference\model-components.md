# 模型组件

每个 SWAP 模型都由像乐高积木一样的组件组成。此模块定义了可用于构建 SWAP 模型的所有组件。

## 一般设置

::: pyswap.components.metadata
::: pyswap.components.simsettings

## 气象设置

::: pyswap.components.meteorology

## 作物设置

::: pyswap.components.crop

### 作物数据库

::: pyswap.db.cropdb

## 灌溉

::: pyswap.components.irrigation

## 土壤水

::: pyswap.components.soilwater

## 排水

::: pyswap.components.drainage

## 边界条件

::: pyswap.components.boundary

## 传输

::: pyswap.components.transport

# 数据库

# 绘图

::: pyswap.core.plot
