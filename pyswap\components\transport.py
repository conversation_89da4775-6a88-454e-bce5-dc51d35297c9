# mypy: disable-error-code="call-overload, misc, override"

from typing import Literal as _Literal

from pydantic import Field as _Field

from pyswap.components.tables import INITSOILTEMP, SOILTEXTURES
from pyswap.core.basemodel import PySWAPBaseModel as _PySWAPBaseModel
from pyswap.core.fields import (
    Decimal2f as _Decimal2f,
    String as _String,
    Table as _Table,
)
from pyswap.core.valueranges import YEARRANGE as _YEARRANGE
from pyswap.utils.mixins import (
    SerializableMixin as _SerializableMixin,
    YAMLValidatorMixin as _YAMLValidatorMixin,
)

__all__ = ["HeatFlow", "SoluteTransport", "SOILTEXTURES", "INITSOILTEMP"]


class HeatFlow(_PySWAPBaseModel, _SerializableMixin, _YAMLValidatorMixin):
    """SWAP 模拟的热流设置。

    属性：
        swhea (Literal[0, 1]): 热流开关。
        swcalt (Optional[Literal[1, 2]]):
            * 1 - 解析方法
            * 2 - 数值方法
        tampli (Optional[Decimal2f]): 土壤表面年温度波幅 [0..50 oC, R]
        tmean (Optional[Decimal2f]): 土壤表面年平均温度 [-10..30 oC, R]
        timref (Optional[Decimal2f]): 正弦温度波达到峰值的时间 [0..366.0 d, R]
        ddamp (Optional[Decimal2f]): 土壤温度波的衰减深度 [1..500 cm, R]
        swtopbhea (Optional[Literal[1, 2]]): 定义顶部边界条件
            * 1 - 使用气象输入文件中的气温作为顶部边界
            * 2 - 使用测量的表层土壤温度作为顶部边界
        tsoilfile (Optional[str]): 包含土壤表面温度的输入文件名，不带 .TSS 扩展名
        swbotbhea (Optional[Literal[1, 2]]): 定义底部边界条件
            * 1 - 无热通量
            * 2 - 规定底部温度
        soiltextures (Optional[Table]): 每个物理土壤层的土壤质地（g/g 矿物部分）和有机质含量（g/g 干土）
        initsoil (Optional[Table]): 初始温度 TSOIL [-50..50 oC, R] 作为土壤深度 ZH [-100000..0 cm, R] 的函数
        bbctsoil (Optional[Table]): 底部边界温度 TBOT [-50..50 oC, R] 作为日期 DATET [日期] 的函数
    """

    swhea: _Literal[0, 1] | None = None
    swcalt: _Literal[1, 2] | None = None
    tampli: _Decimal2f | None = _Field(None, ge=0, le=50)
    tmean: _Decimal2f | None = _Field(None, ge=-10, le=30)
    timref: _Decimal2f | None = _Field(None, **_YEARRANGE)
    ddamp: _Decimal2f | None = _Field(None, ge=1, le=500)
    swtopbhea: _Literal[1, 2] | None = None
    tsoilfile: _String | None = None
    swbotbhea: _Literal[1, 2] | None = None
    soiltextures: _Table | None = None
    initsoiltemp: _Table | None = None
    bbctsoil: _Table | None = None


class SoluteTransport(_PySWAPBaseModel, _SerializableMixin, _YAMLValidatorMixin):
    """溶质传输设置。

    属性：

        swsolu (Literal[0, 1]): 溶质传输模拟开关。
        cpre (Optional[Decimal2f]): 降水中溶质浓度 [0..100 mg/cm3]。
        cdrain (Optional[Decimal2f]): 地表水中溶质浓度 [0..100 mg/cm3]。
        swbotbc (Optional[Literal[0, 1, 2]]): 向上流（渗流）情况下的地下水浓度开关。
        cseep (Optional[Decimal2f]): 地表水中溶质浓度 [0..100 mg/cm3]。
        ddif (Optional[Decimal2f]): 分子扩散系数 [0..10 cm2/d]。
        tscf (Optional[Decimal2f]): 根系对溶质的相对吸收 [0..10]。
        swsp (Optional[Literal[0, 1]]): 开关，考虑溶质吸附。
        frexp (Optional[Decimal2f]): Freundlich 指数 [0..10]。
        cref (Optional[Decimal2f]): 吸附参考溶质浓度 [0..1000 mg/cm3]。
        swdc (Optional[Literal[0, 1]]): 开关，考虑溶质分解。
        gampar (Optional[Decimal2f]): 温度引起的分解减少因子 [0..0.5 /C]。
        rtheta (Optional[Decimal2f]): 潜在分解的最小含水量 [0..0.4 cm3/cm3]。
        bexp (Optional[Decimal2f]): 干燥引起的分解减少指数 [0..2]。
        swbr (Optional[Literal[0, 1]]): 开关，考虑饱和区混合储层。
        daquif (Optional[Decimal2f]): 含水层饱和部分厚度 [0..10000 cm]。
        poros (Optional[Decimal2f]): 含水层孔隙度 [0..0.6]。
        kfsat (Optional[Decimal2f]): 含水层线性吸附系数 [0..100 cm3/mg]。
        decsat (Optional[Decimal2f]): 含水层分解速率 [0..10 /d]。
        cdraini (Optional[Decimal2f]): 地下水中初始溶质浓度 [0..100 mg/cm3]。
        cseeparrtb (Optional[Table]): 地下水浓度随时间变化的表格。
        inissoil (Optional[Table]): 初始溶质浓度随土壤深度变化的表格。
        miscellaneous (Optional[Table]): 杂项参数随土壤深度变化的表格。
    """

    swsolu: _Literal[0, 1] | None = None
    cpre: _Decimal2f | None = _Field(None, ge=0, le=100)
    cdrain: _Decimal2f | None = _Field(None, ge=0, le=100)
    swbotbc: _Literal[0, 1, 2] | None = None
    cseep: _Decimal2f | None = _Field(None, ge=0, le=100)
    ddif: _Decimal2f | None = _Field(None, ge=0, le=10)
    tscf: _Decimal2f | None = _Field(None, ge=0, le=10)
    swsp: _Literal[0, 1] | None = None
    frexp: _Decimal2f | None = _Field(None, ge=0, le=10)
    cref: _Decimal2f | None = _Field(None, ge=0, le=1000)
    swdc: _Literal[0, 1] | None = None
    gampar: _Decimal2f | None = _Field(None, ge=0, le=0.5)
    rtheta: _Decimal2f | None = _Field(None, ge=0, le=0.4)
    bexp: _Decimal2f | None = _Field(None, ge=0, le=2)
    swbr: _Literal[0, 1] | None = None
    daquif: _Decimal2f | None = _Field(None, ge=0, le=10000)
    poros: _Decimal2f | None = _Field(None, ge=0, le=0.6)
    kfsat: _Decimal2f | None = _Field(None, ge=0, le=100)
    decsat: _Decimal2f | None = _Field(None, ge=0, le=10)
    cdraini: _Decimal2f | None = _Field(None, ge=0, le=100)
    cseeparrtb: _Table | None = None
    inissoil: _Table | None = None
    misc: _Table | None = None
