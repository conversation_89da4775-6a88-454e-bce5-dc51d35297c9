{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 加载经典的SWAP模型\n", "\n", "这个Jupyter Notebook演示了如何从标准的ASCII文件将SWAP模型加载到pyswap类中。\n", "\n", "我们像往常一样，首先定义元数据。然后我们可以使用 load_swp 来获取模型的框架，其中包含从初始 .swp 文件加载的所有参数。\n", "\n", "在下面的草地生长示例中，底部边界条件是在一个单独的 .bbc 文件中定义的。在pyswap中，底部边界的所有参数都在同一个类中。因此，load_bbc 函数要么会更新你提供的 BottomBoundary 实例（如下所示），要么会返回一个仅包含 .bbc 文件中设置的参数的新实例。推荐使用第一种方法。"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pyswap as psp"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from pyswap import load_bbc, load_crp, load_dra, load_swp, testcase\n", "\n", "meta = psp.components.Metadata(\n", "    author=\"<PERSON>\",\n", "    institution=\"University of Somewhere\",\n", "    email=\"<EMAIL>\",\n", "    project=\"pySWAP test - hupselbrook\",\n", "    swap_ver=\"4.2\",\n", ")\n", "\n", "ml = load_swp(testcase.get_path(\"grassgrowth\", \"swp\"), meta)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(ml.swp)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ml.bottomboundary = load_bbc(testcase.get_path(\"grassgrowth\", \"bbc\"), ml.bottomboundary)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(ml.swp)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["作物文件以字典的形式提供给 Crop 实例。下面是加载 crp 文件的一种方法"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ml.crop.cropfiles = {\n", "    \"grassd\": load_crp(\n", "        testcase.get_path(\"grassgrowth\", \"grassd\"), crptype=\"grass\", name=\"grassd\"\n", "    )\n", "}"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(ml.crop.cropfiles.get(\"grassd\").crp)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ml.lateraldrainage.drafile = load_dra(testcase.get_path(\"grassgrowth\", \"dra\"))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ml.meteorology.metfile = psp.components.meteorology.metfile_from_csv(\n", "    \"260.met\", testcase.get_path(\"grassgrowth\", \"met\")\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ml.get_inputs()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["result = ml.run(silence_warnings=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["result.yearly_summary"]}], "metadata": {"kernelspec": {"display_name": "pyswap-U62T3krn-py3.11", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 2}