import json
import shutil
from pathlib import Path

import pytest
from typer.testing import <PERSON><PERSON><PERSON><PERSON><PERSON>

# Replace with the actual name of your script file
from pyswap.core.cli.cli import app

runner = CliRunner()


@pytest.fixture(scope="function")
def setup_and_teardown():
    # Setup: Define the folder and ensure it's clean
    folder_name = "TestProjectFolder"
    folder_path = Path.cwd() / folder_name

    # Clean up any existing folder
    if folder_path.exists():
        shutil.rmtree(folder_path)

    yield folder_path

    # Teardown: Clean up after the test
    if folder_path.exists():
        shutil.rmtree(folder_path)


def test_cli_init_script(setup_and_teardown):
    folder_path = setup_and_teardown

    inputs = "\n".join([
        "TestProject",
        "1.0",
        "John Doe",
        "XYZ University",
        "<EMAIL>",
        "No comments",
        "TestProjectFolder",
    ])

    result = runner.invoke(app, ["init", "--script"], input=inputs)
    assert result.exit_code == 0

    main_script_path = folder_path / "models" / "main.py"
    assert main_script_path.exists(), "main.py 未创建。"

    with open(main_script_path) as f:
        content = f.read()
        assert "metadata = psp.components.Metadata(" in content, (
            "main.py 的内容不正确。"
        )


def test_cli_init_notebook(setup_and_teardown):
    folder_path = setup_and_teardown

    inputs = "\n".join([
        "TestProject",
        "1.0",
        "John Doe",
        "XYZ University",
        "<EMAIL>",
        "No comments",
        "TestProjectFolder",
    ])

    result = runner.invoke(app, ["init", "--notebook"], input=inputs)
    assert result.exit_code == 0

    notebook_path = folder_path / "models" / "main.ipynb"
    assert notebook_path.exists(), "main.ipynb 未创建。"

    with open(notebook_path) as f:
        notebook_content = json.load(f)

        code_cell_source = notebook_content["cells"][2]["source"]
        full_code_content = "".join(code_cell_source)
        assert "metadata = ps.Metadata(" in full_code_content, (
            "main.ipynb 的内容不正确。"
        )


def test_git_initialization_and_gitignore(setup_and_teardown):
    folder_path = setup_and_teardown

    inputs = "\n".join([
        "TestProject",
        "1.0",
        "John Doe",
        "XYZ University",
        "<EMAIL>",
        "No comments",
        "TestProjectFolder",
    ])

    result = runner.invoke(app, ["init", "--script", "--notebook"], input=inputs)
    assert result.exit_code == 0

    git_dir_path = folder_path / ".git"
    assert git_dir_path.exists() and git_dir_path.is_dir(), (
        ".git 目录未创建，Git 仓库未初始化。"
    )

    gitignore_path = folder_path / ".gitignore"
    assert gitignore_path.exists(), ".gitignore 文件未创建。"

    with open(gitignore_path) as f:
        gitignore_content = f.read()
        expected_patterns = [
            "__pycache__/",
            "*.pyc",
            "*.pyo",
            "*.pyd",
            ".env",
            "venv/",
            "env/",
            "*.env",
            "*.ipynb_checkpoints/",
            "data/",
        ]

        for pattern in expected_patterns:
            assert pattern in gitignore_content, (
                f"在 .gitignore 文件中未找到预期模式 '{pattern}'。"
            )
