"""SWAP 模型组件。

此模块包含 pySWAP 模型的组件。每个组件都实现为一个继承自 Pydantic BaseModel 的类。
这些组件用于存储模型的输入数据，并提供将数据转换为 SWAP 模型所需格式的方法。
"""

from pyswap.components import (
    boundary,
    crop,
    drainage,
    irrigation,
    meteorology,
    simsettings,
    soilwater,
    transport,
)
from pyswap.components.metadata import Metadata

__all__ = [
    "boundary",
    "crop",
    "drainage",
    "irrigation",
    "meteorology",
    "simsettings",
    "soilwater",
    "transport",
    "metadata",
    "Metadata",
]
