{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 使用 HDF5 数据库\n", "\n", "本教程展示了如何将模型及其结果存入和取出 pySWAP 中集成的 HDF5 数据库（[查看更多](/user-guide/hdf5/)）。 我们将要：\n", "- 加载一个测试案例模型。\n", "- 创建一个 HDF5 数据库并在其中保存一个模型。\n", "- 从 HDF5 数据库中检索一个模型。\n", "- 修改模型并将其版本保存回数据库。\n", "- 比较结果。\n", "\n", "我们还将看到如何并行运行一系列模型。"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from pyswap import db, run_parallel, testcase\n", "\n", "# 获取 Model 对象并运行它\n", "ml = testcase.get(\"h<PERSON><PERSON><PERSON>\")\n", "result = ml.run()\n", "result.csv.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 创建一个接口对象并保存模型和结果\n", "f = db.HDF5(filename=\"data.h5\")\n", "f.save_model(model=ml, result=result)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 检查已保存的模型\n", "\n", "我们现在可以检查数据库中的内容。 通过调用 `.list_models` 属性，我们将得到一个字典，其中的键是项目，值是项目内部模型的列表。"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["f.list_models"]}, {"cell_type": "markdown", "metadata": {}, "source": ["要从数据库中检索模型，请使用 `.load()` 方法。 它将"]}, {"cell_type": "markdown", "metadata": {"vscode": {"languageId": "markdown"}}, "source": ["## 加载模型\n", "\n", "从上面您可以看到模型已保存到 .h5 文件中。 如果文件已经存在（例如，一个包含您其他项目的文件），您将收到一条警告消息，并且程序将跳过在数据库中创建额外元素的操作。\n", "\n", "现在我们将要：\n", "- 从 HDF5 文件中加载一个特定的模型（或一个项目中的所有模型）。\n", "- 创建已加载模型的副本并修改其参数以进行另一次运行。"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["db = f.load(\"psp test - hupselbrook\", load_results=True)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["如下所示，`.load()` 的结果是一个字典，其中键是模型版本名称，值是 `Model` 和 `Result` 对象的元组（如果 `load_results=False`，则为 `Model` 和 None）。"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["db"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 创建模型变体\n", "\n", "我们可以通过创建初始模型的副本并更新一个或多个变量来创建初始模型的变体。"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 没什么特别原因，我们将创建38个新版本，其中每个版本的排水深度增加2厘米\n", "version = {f\"drain_down_{i * 5}\": {\"zbotdr\": -80.0 - i * 5} for i in range(1, 10)}"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ml2 = db.get(\"base\")[0]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["现在我们故意让事情变得更复杂一点，以便额外展示如何更新嵌套对象。 上面我们定义了一个字典，其中的键是新的模型版本名称，值是给定属性的新值。 由于我们不能直接更新嵌套对象的属性，我们需要分步进行：\n", "- 首先，我们从初始模型中创建 DraFile 对象的新的更新副本\n", "- 然后，我们从初始模型中创建 Drainage 对象的副本\n", "- 最后，我们用更新后的 lateraldrainage 属性创建3个模型的副本。\n", "\n", "我们可以使用字典推导式来获取一个更新后的 `DraFile` 对象字典，该字典嵌套在 lateraldrainage 中。 `.update()` 方法返回更新后的副本。"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["versions = {\n", "    key: ml2.lateraldrainage.drafile.update(value) for key, value in version.items()\n", "}"]}, {"cell_type": "markdown", "metadata": {}, "source": ["现在我们用新的 `DraFile` 对象更新 lateraldrainage 属性来创建新模型。 下面的列表推导式返回一个用 `Drainage` 对象副本更新过的新 `Model` 对象列表："]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["mls = [\n", "    ml2.update({\n", "        \"version\": key,\n", "        \"lateraldrainage\": ml2.lateraldrainage.update({\"drafile\": value}),\n", "    })\n", "    for key, value in versions.items()\n", "]\n", "print(\"There are\", len(mls), \"model versions\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 并行运行并保存在 .h5 文件中\n", "\n", "pyswap 现在提供了一种简单的方法来并行化运行模型。 您需要使用 run_parallel() 函数，并向其提供一个模型对象列表，而不是调用 .run() 方法。 该函数会返回一个 Result 对象列表。"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["results = run_parallel(mls, silence_warnings=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["_ = [\n", "    f.save_model(model=ml, result=result)\n", "    for ml, result in zip(mls, results, strict=False)\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["f.list_models"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 可视化结果\n", "\n", "现在您可以通过绘制主要的输出 pandas.DataFrame 对象来观察结果，就像您通常所做的那样。"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["result.csv[\"GWL\"].plot()\n", "_ = [result.csv[\"GWL\"].plot() for result in results]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 删除对象\n", "\n", "您可以使用 `.delete()` 方法从 hdf5 数据库中删除对象。 您可以删除特定的模型或整个项目。 此方法仅适用于少量删除，因为目前它不执行 HDF5 文件的重打包操作（对象被删除但磁盘空间未被释放）。 对于大量删除，请考虑创建一个新的 HDF5 文件，并仅保​​存您想保留的模型。"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["f.list_models"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["f.delete(project=\"psp test - hupselbrook\", model=\"base\")\n", "f.list_models"]}, {"cell_type": "markdown", "metadata": {}, "source": ["您也可以删除整个项目。 这将从数据库中删除对象，但 .h5 文件的大小将保持不变。 因此，对于大量删除，最好完全替换该文件。"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["f.delete(project=\"psp test - hupselbrook\")\n", "f.list_models"]}], "metadata": {"kernelspec": {"display_name": "pyswap-U62T3krn-py3.11", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 2}