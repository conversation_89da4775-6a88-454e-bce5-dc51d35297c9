"""用于微调 pySWAP 对象序列化为 SWAP 格式 ASCII 的函数。

更复杂的序列化逻辑，如果直接在 Annotated 字段定义（pyswap.core.fields 模块）中作为 lambda 函数实现会很笨重，
因此在 serializers 模块（pyswap.core.serializers）中定义。
这些函数将对象转换为有效 SWAP 格式的字符串。

此模块中的序列化器：
    serialize_table: 将 DataFrame 转换为字符串。
    serialize_arrays: 将 DataFrame 转换为不带标题和前导换行符的字符串。
    serialize_csv_table: 将 DataFrame 转换为 CSV 格式的字符串。
    serialize_object_list: 将对象列表转换为字符串。
    serialize_day_month: 将日期对象转换为仅包含日期和月份的字符串。

"""

from datetime import date

from pandas import DataFrame, DatetimeIndex


def serialize_table(table: DataFrame) -> str:
    """将 DataFrame 转换为字符串。

    参数：
        table: 要序列化的 DataFrame。

    结果：
        >>> ' A  B\n 1  4\n 2  5\n 3  6\n'
    """
    return f"{table.to_string(index=False)}\n"


def serialize_arrays(table: DataFrame) -> str:
    """将 DataFrame 转换为不带标题和前导换行符的字符串。

    参数：
        table: 要序列化的 DataFrame。

    结果：
        >>> 'ARRAYS = \n1 4\n2 5\n3 6\n\n'
    """
    return f"\n{table.to_string(index=False, header=False)}\n"


def serialize_csv_table(table: DataFrame) -> str:
    """将 DataFrame 转换为 CSV 格式的字符串。

    此序列化器专门用于以 SWAP 中使用的 .met 文件格式输出数据。

    参数：
        table: 要序列化的 DataFrame。
    """
    if isinstance(table.index, DatetimeIndex):
        table["DD"] = table.index.day
        table["MM"] = table.index.month
        table["YYYY"] = table.index.year
        required_order = [
            "Station",
            "DD",
            "MM",
            "YYYY",
            "RAD",
            "Tmin",
            "Tmax",
            "HUM",
            "WIND",
            "RAIN",
            "ETref",
            "WET",
        ]
        table = table[required_order]

    table.loc[:, "Station"] = table.Station.apply(
        lambda x: f"'{x}'" if not str(x).startswith("'") else x
    )
    return table.to_csv(index=False, lineterminator="\n")


def serialize_day_month(value: date) -> str:
    """将日期对象序列化为仅包含日期和月份的字符串。

    参数：
        value: 要序列化的日期对象。

    结果：
        >>> '01 01'
    """
    return value.strftime("%d %m")


def serialize_decimal(precision: int):
    def decimal_serializer(v, info):
        return f"{round(v, precision):.{precision}f}"

    return decimal_serializer
