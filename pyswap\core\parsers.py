"""将 SWAP 格式的 ascii 文件解析为 pySWAP 对象的函数。

pySWAP 能够直接与经典的 SWAP 输入文件交互。
此模块中定义的解析器用于 `pyswap.core.fields` 模块中定义的自定义字段验证器。
这些函数将 SWAP 格式的 ascii 文件转换为（或反序列化）pySWAP 对象。

此模块中的解析器：
    parse_string_list: 将 SWAP 字符串列表转换为字符串列表。
    parse_quoted_string: 确保从源中删除不必要的引号。
    parse_day_month: 将字符串转换为仅包含日期和月份的日期对象。
"""

from datetime import date


def parse_string_list(value: str) -> str:
    """将 SWAP 字符串列表转换为字符串列表。"""
    if isinstance(value, list):
        return value
    if isinstance(value, str):
        return value.strip("'").split(",")


def parse_float_list(value: str) -> str:
    """将 SWAP 字符串列表转换为字符串列表。"""
    if isinstance(value, list):
        return value
    if isinstance(value, str):
        return value.strip("'").split(" ")


def parse_int_list(value: str) -> str:
    """将 SWAP 字符串列表转换为字符串列表。"""
    if isinstance(value, list):
        return value
    if isinstance(value, str):
        return value.strip("'").split(" ")


def parse_decimal(value: str) -> str:
    """删除 Fortran 风格的小数点。"""
    if isinstance(value, str):
        value = value.lower().replace("d", "e")
    return float(value)


def parse_quoted_string(value: str) -> str:
    """确保从源中删除不必要的引号。"""
    if isinstance(value, str):
        return value.strip("'")
    msg = "Invalid type. Expected string"
    raise ValueError(msg)


def parse_day_month(value: str | date) -> date:
    """将字符串转换为仅包含日期和月份的日期对象。"""
    msg = "Invalid day-month format. Expected 'DD MM'"
    if isinstance(value, date):
        return value
    if isinstance(value, str):
        try:
            day, month = map(int, value.split())
            return date(date.today().year, month, day)
        except (ValueError, TypeError):
            raise ValueError(msg) from None
    raise ValueError(msg)
