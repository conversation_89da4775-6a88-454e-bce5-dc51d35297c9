# 数据结构

SWAP 模型输入是简单的格式化字符串，保存在纯 ASCII 文件中。输入有三种主要格式/类型：

- **键值对**：变量名后跟等号和格式化值。
- **表格**：带有列名的数据行。列由空格分隔，行由换行符分隔。它们没有标签（变量名后跟等号）。
- **数组**：没有列名的数据行。列由空格分隔，行由换行符分隔。它们前面有一个标签，数据从下一行开始。

所有参数都分组到表示 SWAP 模型不同组件的类中。

## Classes and attribute types

`pyswap` 模型（类或对象）是 [Pydantic](https://docs.pydantic.dev/latest/) `BaseModel` 的子类。Pydantic 是一个强大的验证和序列化库，可确保提供的数据类型正确且在正确范围内。如果可能，输入将被强制转换为正确的格式；否则，将引发异常。下面是一个 pyswap 类定义的示例：

```py
class SnowAndFrost(_PySWAPBaseModel, _SerializableMixin, _YAMLValidatorMixin):
    """Snow and frost settings for the model.

    属性：
        swsnow (Literal[0, 1])：计算积雪和融雪的开关。
        swfrost (Literal[0, 1])：开关，在霜冻情况下减少土壤水流。
        snowinco (Optional[Decimal2f])：初始雪水当量 [0..1000 厘米]。
        teprrain (Optional[Decimal2f])：所有降水为雨的温度上限 [0..10 摄氏度]。
        teprsnow (Optional[Decimal2f])：所有降水为雪的温度下限 [-10..0 摄氏度]。
        tfroststa (Optional[Decimal2f])：土壤水通量开始减少的土壤温度（摄氏度）[-10.0..5.0 摄氏度]。
        tfrostend (Optional[Decimal2f])：土壤水通量结束减少的土壤温度（摄氏度）[-10.0..5.0 摄氏度]。
    """

    swsnow: _Literal[0, 1] | None = None
    swfrost: _Literal[0, 1] | None = None
    snowinco: _Decimal2f | None = _Field(default=None, ge=0, le=1000)
    teprrain: _Decimal2f | None = _Field(default=None, ge=0, le=10)
    teprsnow: _Decimal2f | None = _Field(default=None, ge=-10, le=0)
    tfroststa: _Decimal2f | None = _Field(default=None, ge=-10, le=5)
    tfrostend: _Decimal2f | None = _Field(default=None, ge=-10, le=5)
```

每个类的属性通常对应于 SWAP 输入文件中的变量名。每个属性都有一个指定的类型和验证规则（例如，下限和上限）。其中许多类型是带有特定序列化和验证规则的自定义注释类型。这些规则告诉类构造函数在提供值时如何解释值以及在将数据保存到文件时如何格式化数据。

下面是一个带有其验证和序列化规则的类型示例。有关更多详细信息，请参阅 [验证和序列化部分](/reference/developer/#validation_and_serialization)。

```py
FloatList = Annotated[
    list[float] | str,
    AfterValidator(parse_float_list),  # (1)!
    PlainSerializer(
        lambda x: " ".join([f"{Decimal(f):.2f}" for f in x]),
        return_type=str,
        when_used="json",  # (2)!
    ),
]
"""将浮点数列表序列化为以空格分隔元素的字符串。"""
```

1. 通常，输入应该是一个浮点数列表，但如果以字符串形式提供，`parse_float_list` 会将其转换为列表。这在直接从 SWAP 输入文件传递格式化字符串时很有用。有关详细信息，请参阅 [参考](/reference/developer/#pyswap.core.parsers.parse_float_list)。
2. `PlainSerializer` 确保在序列化期间（当输入变量写入文件时），列表使用 lambda 函数转换为正确格式化的字符串，表示具有两位小数的浮点数。

Here is another field example:

```py
Decimal2f = Annotated[
    float | str,
    BeforeValidator(parse_decimal),  # (1)!
    PlainSerializer(serialize_decimal(precision=2), return_type=str, when_used="json"),  # (2)!
]
"""将浮点数序列化为具有 2 位小数的字符串。"""
```

1. [parse_decimal](/reference/developer/#pyswap.core.parsers.parse_decimal) 确保输入文件中的字符串确实是浮点数。它会删除任何 Fortran 兼容的表示法，例如“d”或“e”，以确保其强制转换为浮点数。
2. [serialize_decimal](/reference/developer/#pyswap.core.serializers.serialize_decimal) 确保浮点数以两位小数保存到输入文件中。

## Tables

表格数据通过将其分配给类属性的方式与键值对相同。这些属性的类型可以是数组或表格，具体取决于该特定变量所需的序列化类型。对于输入和输出，`pyswap` 都依赖于 pandas DataFrames。

### Input

表格输入以 DataFrames 的形式提供，在运行模型之前，应验证这些 DataFrames 是否存在某些列、上限和下限等。`pyswap` 为此使用了 [Pandera](https://pandera.readthedocs.io/en/stable/) 库，该库与 Pydantic 非常相似。目前，验证 DataFrames 的方法是使用相应模式的 `.create()` 方法。模式本质上是包含有关所需列和其定义中所需数据类型信息的类。

Below is an example of a SOILPROFILE schema definition and then generated using the `.create()` method:

```py
class SOILPROFILE(BaseTableModel):
    """Vertical discretization of the soil profile

    属性：
        ISUBLAY: Series[int]：子层编号，从土壤表面开始，从 1 开始 [1..MACP, I]。
        ISOILLAY: Series[int]：土壤物理层编号，从土壤表面开始，从 1 开始 [1..MAHO, I]。
        HSUBLAY: Series[float]：子层高度 [0..1.d4 厘米, R]。
        HCOMP: Series[float]：子层中隔室的高度 [0.0..1000.0 厘米, R]。
        NCOMP: Series[int]：子层中隔室的数量（注意 NCOMP = HSUBLAY/HCOMP）[1..MACP, I]。
    """

    ISOILLAY: Series[int] = pa.Field(ge=1)
    ISUBLAY: Series[int] = pa.Field(ge=1)
    HSUBLAY: Series[float] = pa.Field(ge=0.0, le=1.0e4)
    HCOMP: Series[float] = pa.Field(ge=0.0, le=1.0e3)
    NCOMP: Series[int] = pa.Field(ge=1)
```

Here's how to create a table:

```py
import pyswap as psp  # (1)!

soil_profile = psp.components.soilwater.SOILPROFILE.create({
    "ISUBLAY": [1, 2, 3, 4],  # (2)!
    "ISOILLAY": [1, 1, 2, 2],
    "HSUBLAY": [10.0, 20.0, 30.0, 140.0],
    "HCOMP": [1.0, 5.0, 5.0, 10.0],
    "NCOMP": [10, 4, 6, 14],
})
```

1. 表格模式从与其应分配到的类相同的组件组中导入。因此，`SOILPROFILE` 将从 `soilwater` 组件中导入。
2. 例如，如果我们将 `ISUBLAY` 列中的一个元素设置为 0，我们将收到错误。

### Output

模型的所有输出都捕获在 `Result` 对象中。这包括请求的 ASCII 输出文件（例如 .blc）和 CSV 文件。CSV 输出文件会自动转换为带有 DateTime 索引的 pandas DataFrames。

!!! note

    ASCII 输出文件（例如 .blc）将在未来版本的 SWAP 和 pyswap 中弃用。
