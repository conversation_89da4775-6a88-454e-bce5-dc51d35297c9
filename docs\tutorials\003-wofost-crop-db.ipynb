{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# WOFOST crop database\n", "\n", "There is a GitHub repository containing calibtated parameters for a range of varieties of crops. These parameters were used, e.g., in the Hupselbrook test case provided with the SWAP model for potato variety. As these are reusable, it made sense to tap into that database to make creation of valid crop files for pySWAP easier.\n", "\n", "In this notebook, we will create a .crp file for potato variety used in the h<PERSON>elbrook test case."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pyswap as psp"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Exploring the database\n", "\n", "pySWAP contains a simple wrapper around A. Wit's crop database. We can use built in classes to get an overview of available data. Then we can check which crop types are available in the database."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cropdb = psp.db.WOFOSTCropDB()\n", "cropdb.croptypes"]}, {"cell_type": "markdown", "metadata": {}, "source": ["When we call `load_crop_file` on the database object with the name of a specific crop type, we will get a WOFOSTCropFile object. We can then inspect that object to see the file's metadata, parameters in individual sections, available ecotypes and varieties."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cropfile = cropdb.load_crop_file(\"rice\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cropfile.metadata"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(cropfile.ecotypes)\n", "print(cropfile.varieties)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["rice_501 = cropfile.get_variety(\"Rice_501\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The new object is `CropVariety` object. From here you can view the metadata of the particular variety or obtain just the parameters as a dictionary. Mind that at this stage, all tables are just arrays of numbers, the same way as it is formatted in the .yaml files. pySWAP automatically converts them in tables when they are used to populate the parameters of crop settings classes."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Using the CropVariety object to populate the crop settings class\n", "\n", "The point of having this database integration is to enable populating some of the wofost crop parameters used by swap directly from the files. A lot of the parameters we still have to set manually, namely Preparation, ScheduledIrrigation, Interception, OxygenStress, DroughtStress, etc."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["potato_prep = psp.components.crop.Preparation(\n", "    swprep=0,\n", "    swsow=0,\n", "    swgerm=2,\n", "    tsumemeopt=170.0,\n", "    tbasem=3.0,\n", "    teffmx=18.0,\n", "    hdrygerm=-500.0,\n", "    hwetgerm=-100.0,\n", "    zgerm=-10.0,\n", "    agerm=203.0,\n", "    dvsend=2.0,\n", "    swharv=0,\n", ")\n", "\n", "scheduled_irrigation = psp.components.irrigation.ScheduledIrrigation(schedule=0)\n", "interception = psp.components.crop.Interception(swinter=1, cofab=0.25)\n", "\n", "potato_chtb = psp.components.crop.CHTB.create({\n", "    \"DVS\": [0.0, 1.0, 2.0],\n", "    \"CH\": [\n", "        1.0,\n", "        40.0,\n", "        50.0,\n", "    ],\n", "})\n", "\n", "potato_rdctb = psp.components.crop.RDCTB.create({\n", "    \"RRD\": [0.0, 1.0],\n", "    \"RDENS\": [1.0, 0.0],\n", "})\n", "\n", "potato_ox_stress = psp.components.crop.OxygenStress(\n", "    swoxygen=1,\n", "    swwrtnonox=1,\n", "    aeratecrit=0.5,\n", "    hlim1=-10.0,\n", "    hlim2u=-25.0,\n", "    hlim2l=-25.0,\n", "    swrootradius=2,\n", "    root_radiuso2=0.00015,\n", ")\n", "\n", "potato_dr_stress = psp.components.crop.DroughtStress(\n", "    swdrought=1,\n", "    hlim3h=-300.0,\n", "    hlim3l=-500.0,\n", "    hlim4=-10000.0,\n", "    adcrh=0.5,\n", "    adcrl=0.1,\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Then, we can load the potato_701 variety from the WOFOST database and create the CropDevelopmentSettings object like so:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["db = psp.db.WOFOSTCropDB()\n", "potato = db.load_crop_file(\"potato\")\n", "potato_params = potato.get_variety(\"Potato_701\")\n", "\n", "potato_cropdev_settings = psp.components.crop.CropDevelopmentSettingsWOFOST(\n", "    wofost_variety=potato_params,\n", "    swcf=2,\n", "    dvs_ch=potato_chtb,\n", "    albedo=0.19,\n", "    laiem=0.0589,\n", "    ssa=0.0,\n", "    kdif=1.0,\n", "    rsc=207.0,\n", "    rsw=0.0,\n", "    kdir=0.75,\n", "    eff=0.45,\n", "    swrd=2,\n", "    rdc=50.0,\n", "    swdmi2rd=1,\n", "    rdctb=potato_rdctb,\n", ")\n", "\n", "# By calling the `update_from_wofost method`, the CropDevelopmentSettingsWOFOST object\n", "# will be updated with the values from the WOFOST crop file and the model will be evaluated,\n", "# to make sure that some of the required settings are not missing.\n", "potato_cropdev_settings.update_from_wofost()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Further we proceed with creation of the `CropFile` object, which goes directly to the `Model`. You can observe the content of the crop file by calling `CropFile.crp` property."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["crppotato = psp.components.crop.CropFile(\n", "    name=\"potatod\",\n", "    prep=potato_prep,\n", "    cropdev_settings=potato_cropdev_settings,\n", "    oxygenstress=potato_ox_stress,\n", "    droughtstress=potato_dr_stress,\n", "    interception=interception,\n", "    scheduledirrigation=scheduled_irrigation,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(crppotato.crp)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The WOFOST database integration allows you draw calibrated settings from the database into pySWAP. If you wish to change some of the settings, you should do it by updating the base pySWAP classes (e.g., CropDevalopmentSettings), like so:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["potato_cropdev_settings_tsum1_up = potato_cropdev_settings.update({\"tsum1\": 900})"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["crppotato_tsum1_up = crppotato.update({\n", "    \"cropdev_settings\": potato_cropdev_settings_tsum1_up\n", "})"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(crppotato_tsum1_up.cropdev_settings.tsum1)"]}], "metadata": {"kernelspec": {"display_name": "pyswap-U62T3krn-py3.11", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 2}