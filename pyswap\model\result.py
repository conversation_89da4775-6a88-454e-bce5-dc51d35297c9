# mypy: disable-error-code="no-any-return"


"""捕获模型结果。

模型运行后，结果存储在 `Result` 对象中。该对象存储日志文件、输出文件和警告。
输出是一个字典，其中键是文件扩展名，值是文件内容。
还有一些计算属性，使最常见的输出格式易于访问。

类：
    Result: 模型运行的结果。
"""

import re

from pandas import DataFrame
from pydantic import BaseModel, ConfigDict, Field, computed_field

__all__ = ["Result"]


class Result(BaseModel):
    """模型运行的结果。

    属性：
        log (str): 模型运行的日志文件。
        output (DataFrame): 模型运行的输出文件。
        warning (List[str]): 模型运行的警告。

    属性：
        ascii (dict): ASCII 格式的输出。
        csv (DataFrame): CSV 格式的输出。
        csv_tz (DataFrame): 带有深度的 CSV 格式输出。
        iteration_stats (str): 返回日志中迭代统计信息的部分。
        blc_summary (str): 如果存在，则为 .blc 文件。
        yearly_summary (DataFrame): 所有输出变量的年度总和。如果输出文件格式中不包含 CSV，则会返回错误。
    """

    log: str | None = Field(default=None, repr=False)
    output: dict | None = Field(default_factory=dict, repr=False)
    warning: list[str] | None = Field(default=None, repr=False)

    model_config = ConfigDict(
        arbitrary_types_allowed=True, validate_assignment=True, extra="forbid"
    )

    @computed_field(return_type=dict, repr=False)
    def ascii(self) -> dict:
        """返回所有 ASCII 格式的输出。"""
        return {k: v for k, v in self.output.items() if not k.endswith("csv")}

    @computed_field(return_type=DataFrame, repr=False)
    def csv(self) -> DataFrame:
        """返回 CSV 格式的输出。"""
        return self.output.get("csv", None)

    @computed_field(return_type=DataFrame, repr=False)
    def csv_tz(self) -> DataFrame:
        """返回带有深度的 CSV 格式输出。"""
        return self.output.get("csv_tz", None)

    @computed_field(return_type=str, repr=False)
    def iteration_stats(self) -> str:
        """打印日志中迭代统计信息的部分。"""
        match = re.search(r".*(Iteration statistics\s*.*)$", self.log, re.DOTALL)
        if match:
            return match.group(1)
        return ""

    @computed_field(return_type=str, repr=False)
    def blc_summary(self) -> str:
        """如果存在，则打印 .blc 文件。"""
        print(self.output.get("blc", None))
        return

    @computed_field(return_type=DataFrame, repr=False)
    def yearly_summary(self) -> DataFrame:
        """返回所有输出变量的年度总和。"""
        if not isinstance(self.csv, DataFrame):
            msg = "输出文件格式中不包含 CSV 文件。"
            raise TypeError(msg)
        return self.csv.resample("YE").sum()
