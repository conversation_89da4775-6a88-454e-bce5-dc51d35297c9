site_name: "pySWAP documentation"

repo_url: https://github.com/zawadzkim/pySWAP
repo_name: zawadzkim/pySWAP

watch:
  - ./pyswap

extra_css:
  - stylesheets/extra.css

theme:
  name: material
  logo: public/logo.webp
  navigation_depth: 2
  features:
    - navigation.instant
    - navigation.instant.progress
    - navigation.tabs
    - navigation.tabs.sticky
    - navigation.sections
    - navigation.top
    - navigation.footer
    # search settings
    - search.suggest
    - search.highlight
    - content.tabs.link
    - content.code.annotate
    - content.code.annotation
    - content.code.copy
  language: en
  palette:
    - scheme: default
      toggle:
        icon: material/toggle-switch-off-outline
        name: "Switch to Dark Mode"
      primary: teal
      accent: purple
    - scheme: slate
      toggle:
        icon: material/toggle-switch-off
        name: "Switch to Light Mode"
      primary: teal
      accent: lime
plugins:
  - search
  - autorefs
  - include-markdown
  - awesome-pages
  - exclude-search:
      exclude:
        - tutorials/
  - mkdocs-jupyter:
      execute: true
  - mkdocstrings:
      handlers:
        python:
          options:
            show_submodules: true
            show_symbol_type_heading: true
            show_symbol_type_toc: true
            show_root_toc_entry: false
            show_root_heading: false
            heading_level: 2

markdown_extensions:
  - admonition
  - codehilite
  - attr_list
  - md_in_html
  - pymdownx.details
  - pymdownx.superfences
  - pymdownx.tabbed:
      alternate_style: true
  - pymdownx.inlinehilite
  - pymdownx.snippets
  - pymdownx.emoji:
      emoji_index: !!python/name:material.extensions.emoji.twemoji
      emoji_generator: !!python/name:material.extensions.emoji.to_svg
  - pymdownx.highlight:
      anchor_linenums: true
      line_spans: __span
      pygments_lang_class: true
  - toc:
      permalink: true
      separator: "_"
      toc_depth: 2
