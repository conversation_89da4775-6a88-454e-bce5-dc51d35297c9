name: Create Release and Publish to Py<PERSON>

on:
  push:
    branches:
      - main
    paths-ignore:
      - "docs/**"

permissions:
  contents: write
  packages: write

jobs:
  release_and_publish:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v3
        with:
          submodules: "recursive"

      - name: Set up Python
        uses: actions/setup-python@v2
        with:
          python-version: "3.11"

      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          pip install poetry
          poetry install

      - name: Extract version from pyproject.toml
        id: extract_version
        run: |
          version=$(poetry version -s)
          echo ::set-output name=version::$version

      - name: Create GitHub Release
        id: create_release
        uses: actions/create-release@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          tag_name: v${{ steps.extract_version.outputs.version }}
          release_name: Release ${{ steps.extract_version.outputs.version }}
          draft: false
          prerelease: false
          generate_release_notes: true

      - name: Publish to PyPI
        env:
          POETRY_PYPI_TOKEN_PYPI: ${{ secrets.PYPI_TOKEN }}
        run: |
          poetry publish --build --username __token__ --password $POETRY_PYPI_TOKEN_PYPI
