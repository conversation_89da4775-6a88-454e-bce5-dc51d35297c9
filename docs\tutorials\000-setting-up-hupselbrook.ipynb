{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Hupselbrook Model Setup\n", "\n", "SWAP plain-text configuration files (.swp, .crp, .dra, .bbc) consist of various switches (parameters with \"SW\" prefix), key-value pairs, and tables. These elements are grouped into sections corresponding to specific parts of the model. For example, the meteorological section includes parameters for climatological calculations, while the crop section handles parameters related to, e.g., crop water uptake. You can see the structure of the classic template of SWAP file inputs in the [wiki section](/wiki/input-files/1-input-files/).\n", "\n", "pyswap is built on Object Oriented Programming principle, similar to [flopy](https://flopy.readthedocs.io/en/stable/) package for MODFLOW model. Different parts of the model, or model components, are represented by separate classes (`GeneralSettings`, `Meteorology`, etc.). These components are then stacked together like lego blocks to build a complete model. This approach allows to easily build variants of a model by modifying or replacing individual components without changing the overall structure.\n", "\n", "<div class=\"admonition tip\">\n", "  <p class=\"admonition-title\">Tip</p>\n", "  <p style=\"margin-top: 10px;\" class=\"admonition-content\">    \n", "    pySWAP is designed with type hinting in mind. When using a code editor like Visual Studio Code, typing `pyswap.` will show hints for available classes and functions. Similarly, hovering over a class signature will display available parameters, value ranges, and documentation.\n", "  </p>\n", "</div>"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from datetime import datetime as dt\n", "\n", "import pyswap as psp"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## General settings\n", "\n", "In pySWAP, each distinct section is represented as a class, similarily to flopy, a popular Python package running MODFLOW models. Therefore, in a sense, defining a model with pySWAP feels similar to defining it in a classic ASCII template, but gives you more options to work with your models further. Finally, when all necessary objects are defined, you create a Model instance. You can also define an empty Model instance, and add each section to that instance after it's created. Let's set up `Metadata` and `GeneralSettings` for the model. We will start with an empty model instance."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# starting with an empty model instance\n", "ml = psp.Model()\n", "\n", "\n", "meta = psp.components.Metadata(\n", "    author=\"<PERSON>\",\n", "    institution=\"University of Somewhere\",\n", "    email=\"<EMAIL>\",\n", "    project=\"pySWAP test - hupselbrook\",\n", "    swap_ver=\"4.2\",\n", ")\n", "\n", "simset = psp.components.simsettings.GeneralSettings(\n", "    tstart=\"2002-01-01\",\n", "    tend=\"2004-12-31\",\n", "    extensions=[\"vap\", \"blc\", \"sba\", \"inc\", \"csv\"],\n", "    nprintday=1,\n", "    swerror=1,\n", "    swscre=0,\n", "    swmonth=1,\n", "    swyrvar=0,\n", "    datefix=\"31 12\",\n", "    inlist_csv=[\n", "        \"rain\",\n", "        \"irrig\",\n", "        \"interc\",\n", "        \"runoff\",\n", "        \"drainage\",\n", "        \"dstor\",\n", "        \"epot\",\n", "        \"eact\",\n", "        \"tpot\",\n", "        \"tact\",\n", "        \"qbottom\",\n", "        \"gwl\",\n", "    ],\n", ")\n", "\n", "# attaching model components to the model instance\n", "ml.metadata = meta\n", "ml.generalsettings = simset"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<div class=\"admonition note\">\n", "  <p class=\"admonition-title\">Note</p>\n", "  <p style=\"margin-top: 10px;\" class=\"admonition-content\">    \n", "    At this stage there is one important difference to explain; you do not need to (and actually cannot) adjust the paths at the beginning. This is because pySWAP runs SWAP in a temporary directory and handles paths automatically. The same goes for file names; the default and frozen file names are \"swap\" for inputs (e.g., drainage file) and \"result\" for output. \n", "  </p>\n", "</div>\n", "\n", "After adding the two sections, you can view how the section would look like as a SWAP-compatible string, or see the current shape of the .swp file by calling `ml.swp` property. You may notice more parameters that you wanted to set. It's because by default, heat flow (`HeatFlow`), solute transport (`SoluteTransport`) and fixed irrigation (`FixedIrrigation`) modules are turned off in the model. There are also default settings for some parameters of <PERSON>'s equation (`RichardsSettings`). To change it, it's enough to define your own objects with desired settings and reassign them in the `ml` instance."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(ml.swp)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Meteorology\n", "\n", "Setting up meteorological section additionally requires providing some climatological data in a specific CSV format. Those data are enclosed in a `File` type object. For meteorological data it's `MetFile` class. Let's create the `Meteorology` object with a `MetFile` attached to it.\n", "\n", "Currently the other built-in method of getting the MetFile is using KNMI service or loading it from a CSV file. Read documentation of `pyswap.components.meteorology` module."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# here we additionally need to load the meteo data from testcase library\n", "from pyswap import testcase\n", "\n", "meteo_data = psp.components.meteorology.metfile_from_csv(\n", "    metfil=\"283.met\", csv_path=testcase.get_path(\"hupselbrook\", \"met\")\n", ")\n", "\n", "meteo = psp.components.meteorology.Meteorology(\n", "    lat=52.0,\n", "    alt=21.0,\n", "    swetr=0,\n", "    metfile=meteo_data,\n", "    swdivide=1,\n", "    swmetdetail=0,\n", "    altw=10.0,\n", "    angstroma=0.25,\n", "    angstromb=0.5,\n", ")\n", "\n", "ml.meteorology = meteo"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Crop\n", "\n", "Crop settings can be defined either fully in your Python script, or can be partly imported from the WOFOST crop database (depending on which crop file type you are going to use). In the Hupselbrook example, the crop rotation settings includes all three: simple setup for maize, detailed WOFOST model for potato and dynamic grass growth. \n", "\n", "Each crop file is set up as `Crop` objects which in turn consists of subsections (`ScheduledIrrigation`, `CropDevelopmentSettingsFixed`, etc). This is identical to how the main `Model` is composed.\n", "\n", "This section is somewhat long, so buckle up..."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Simple (fixed) crop settings for maize"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["maize_prep = psp.components.crop.Preparation(\n", "    swprep=0, swsow=0, swgerm=0, dvsend=3.0, swharv=0\n", ")\n", "\n", "scheduled_irrigation = psp.components.irrigation.ScheduledIrrigation(schedule=0)\n", "\n", "DVS = [0.0, 0.3, 0.5, 0.7, 1.0, 1.4, 2.0]\n", "\n", "# This is one way to create and validate tables in pySWAP.\n", "maize_gctb = psp.components.crop.GCTB.create({\n", "    \"DVS\": DVS,\n", "    \"LAI\": [0.05, 0.14, 0.61, 4.10, 5.00, 5.80, 5.20],\n", "})\n", "\n", "maize_cftb = psp.components.crop.CFTB.create({\n", "    \"DVS\": DVS,\n", "    \"CH\": [1.0, 15.0, 40.0, 140.0, 170.0, 180.0, 175.0],\n", "})\n", "\n", "maize_rdtb = psp.components.crop.RDTB.create({\n", "    \"DVS\": [0.0, 0.3, 0.5, 0.7, 1.0, 2.0],\n", "    \"RD\": [5.0, 20.0, 50.0, 80.0, 90.0, 100.0],\n", "})\n", "\n", "maize_rdctb = psp.components.crop.RDCTB.create({\n", "    \"RRD\": [0.0, 1.0],\n", "    \"RDENS\": [1.0, 0.0],\n", "})\n", "\n", "maize_cropdev_settings = psp.components.crop.CropDevelopmentSettingsFixed(\n", "    idev=1,\n", "    lcc=168,\n", "    kdif=0.6,\n", "    kdir=0.75,\n", "    swgc=1,\n", "    gctb=maize_gctb,\n", "    swcf=2,\n", "    cftb=maize_cftb,\n", "    albedo=0.23,\n", "    rsc=61.0,\n", "    rsw=0.0,\n", "    swrd=1,\n", "    rdtb=maize_rdtb,\n", "    rdctb=maize_rdctb,\n", ")\n", "\n", "maize_ox_stress = psp.components.crop.OxygenStress(\n", "    swoxygen=1,\n", "    swwrtnonox=0,\n", "    aeratecrit=0.5,\n", "    hlim1=-15.0,\n", "    hlim2u=-30.0,\n", "    hlim2l=-30.0,\n", ")\n", "\n", "maize_dr_stress = psp.components.crop.DroughtStress(\n", "    swdrought=1,\n", "    hlim3h=-325.0,\n", "    hlim3l=-600.0,\n", "    hlim4=-8000.0,\n", "    adcrh=0.5,\n", "    adcrl=0.1,\n", ")\n", "\n", "# shared with the fixed crop settings\n", "\n", "maize_interception = psp.components.crop.Interception(swinter=1, cofab=0.25)\n", "\n", "crpmaize = psp.components.crop.CropFile(\n", "    name=\"maizes\",\n", "    prep=maize_prep,\n", "    scheduledirrigation=scheduled_irrigation,\n", "    cropdev_settings=maize_cropdev_settings,\n", "    oxygenstress=maize_ox_stress,\n", "    droughtstress=maize_dr_stress,\n", "    interception=maize_interception,\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## WOFOST settings for potato\n", "\n", "For the WOFOST model, many more parameters are necessary. Therefore we can make use of calibrated parameters for crops from existing databases. pySWAP uses the de Wit's crop database to automatically load some available parameters. The remaining parameters still need to be supplied by the user. We will make use of that database below."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from pyswap import db\n", "\n", "# Load the crop database\n", "db = db.WOFOSTCropDB()\n", "potato = db.load_crop_file(\"potato\")\n", "potato_params = potato.get_variety(\"Potato_701\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["potato_prep = psp.components.crop.Preparation(\n", "    swprep=0,\n", "    swsow=0,\n", "    swgerm=2,\n", "    tsumemeopt=170.0,\n", "    tbasem=3.0,\n", "    teffmx=18.0,\n", "    hdrygerm=-500.0,\n", "    hwetgerm=-100.0,\n", "    zgerm=-10.0,\n", "    agerm=203.0,\n", "    dvsend=2.0,\n", "    swharv=0,\n", ")\n", "\n", "potato_chtb = psp.components.crop.CFTB.create({\n", "    \"DVS\": [0.0, 1.0, 2.0],\n", "    \"CH\": [\n", "        1.0,\n", "        40.0,\n", "        50.0,\n", "    ],\n", "})\n", "\n", "potato_rdctb = psp.components.crop.RDCTB.create({\n", "    \"RRD\": [0.0, 1.0],\n", "    \"RDENS\": [1.0, 0.0],\n", "})\n", "\n", "\n", "potato_cropdev_settings = psp.components.crop.CropDevelopmentSettingsWOFOST(\n", "    wofost_variety=potato_params,\n", "    swcf=2,\n", "    cftb=potato_chtb,\n", "    albedo=0.19,\n", "    laiem=0.0589,\n", "    ssa=0.0,\n", "    kdif=1.0,\n", "    rsc=207.0,\n", "    rsw=0.0,\n", "    kdir=0.75,\n", "    eff=0.45,\n", "    swrd=2,\n", "    rdc=50.0,\n", "    swdmi2rd=1,\n", "    rdctb=potato_rdctb,\n", ")\n", "\n", "potato_cropdev_settings.update_from_wofost()\n", "\n", "potato_ox_stress = psp.components.crop.OxygenStress(\n", "    swoxygen=1,\n", "    swwrtnonox=1,\n", "    aeratecrit=0.5,\n", "    hlim1=-10.0,\n", "    hlim2u=-25.0,\n", "    hlim2l=-25.0,\n", "    swrootradius=2,\n", "    root_radiuso2=0.00015,\n", ")\n", "\n", "potato_dr_stress = psp.components.crop.DroughtStress(\n", "    swdrought=1,\n", "    hlim3h=-300.0,\n", "    hlim3l=-500.0,\n", "    hlim4=-10000.0,\n", "    adcrh=0.5,\n", "    adcrl=0.1,\n", ")\n", "\n", "crppotato = psp.components.crop.CropFile(\n", "    name=\"potatod\",\n", "    prep=potato_prep,\n", "    cropdev_settings=potato_cropdev_settings,\n", "    oxygenstress=potato_ox_stress,\n", "    droughtstress=potato_dr_stress,\n", "    # shared with the fixed crop settings\n", "    interception=maize_interception,\n", "    scheduledirrigation=scheduled_irrigation,\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Dynamic grass model\n", "\n", "This one requires many parameters that are unavailable in the crop database pySWAP uses. Therefore, all tables and parameters have to be defined manually."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["grass_chtb = psp.components.crop.CFTB.create({\n", "    \"DNR\": [0.0, 180.0, 366.0],\n", "    \"CH\": [12.0, 12.0, 12.0],\n", "})\n", "\n", "grass_slatb = psp.components.crop.SLATB.create({\n", "    \"DNR\": [1.00, 80.00, 300.00, 366.00],\n", "    \"SLA\": [0.0015, 0.0015, 0.0020, 0.0020],\n", "})\n", "\n", "amaxtb = psp.components.crop.AMAXTB.create({\n", "    \"DNR\": [1.00, 95.00, 200.00, 275.00, 366.00],\n", "    \"AMAX\": [40.00, 40.00, 35.00, 25.00, 25.00],\n", "})\n", "\n", "grass_tmpftb = psp.components.crop.TMPFTB.create({\n", "    \"TAVD\": [0.00, 5.00, 15.00, 25.00, 40.00],\n", "    \"TMPF\": [0.00, 0.70, 1.00, 1.00, 0.00],\n", "})\n", "grass_tmnftb = psp.components.crop.TMNFTB.create({\n", "    \"TMNR\": [0.0, 4.0],\n", "    \"TMNF\": [0.0, 1.0],\n", "})\n", "\n", "grass_rfsetb = psp.components.crop.RFSETB.create({\n", "    \"DNR\": [1.00, 366.00],\n", "    \"RFSE\": [1.0000, 1.0000],\n", "})\n", "\n", "grass_frtb = psp.components.crop.FRTB.create({\n", "    \"DNR\": [1.00, 366.00],\n", "    \"FR\": [0.3000, 0.3000],\n", "})\n", "\n", "grass_fltb = psp.components.crop.FLTB.create({\n", "    \"DNR\": [1.00, 366.00],\n", "    \"FL\": [0.6000, 0.6000],\n", "})\n", "\n", "grass_fstb = psp.components.crop.FSTB.create({\n", "    \"DNR\": [1.00, 366.00],\n", "    \"FS\": [0.4000, 0.4000],\n", "})\n", "\n", "grass_rdrrtb = psp.components.crop.RDRRTB.create({\n", "    \"DNR\": [1.0, 180.0, 366.0],\n", "    \"RDRR\": [0.0, 0.02, 0.02],\n", "})\n", "\n", "grass_rdrstb = psp.components.crop.RDRSTB.create({\n", "    \"DNR\": [1.0, 180.0, 366.0],\n", "    \"RDRS\": [0.0, 0.02, 0.02],\n", "})\n", "\n", "grass_rlwtb = psp.components.crop.RLWTB.create({\n", "    \"RW\": [300.00, 2500.00],\n", "    \"RL\": [20.0, 40.0],\n", "})\n", "\n", "grass_rdctb = psp.components.crop.RDCTB.create({\n", "    \"RRD\": [0.0, 1.0],\n", "    \"RDENS\": [1.0, 0.0],\n", "})\n", "\n", "grass_settings = psp.components.crop.CropDevelopmentSettingsGrass(\n", "    swcf=2,\n", "    cftb=grass_chtb,\n", "    albedo=0.23,\n", "    rsc=100.0,\n", "    rsw=0.0,\n", "    tdwi=1000.00,\n", "    laiem=0.63000,\n", "    rgrlai=0.00700,\n", "    swtsum=1,\n", "    ssa=0.0004,\n", "    span=30.00,\n", "    tbase=0.00,\n", "    slatb=grass_slatb,\n", "    kdif=0.60,\n", "    kdir=0.75,\n", "    eff=0.50,\n", "    amaxtb=amaxtb,\n", "    tmpftb=grass_tmpftb,\n", "    tmnftb=grass_tmnftb,\n", "    cvl=0.6850,\n", "    cvr=0.6940,\n", "    cvs=0.6620,\n", "    q10=2.0000,\n", "    rml=0.0300,\n", "    rmr=0.0150,\n", "    rms=0.0150,\n", "    rfsetb=grass_rfsetb,\n", "    frtb=grass_frtb,\n", "    fltb=grass_fltb,\n", "    fstb=grass_fstb,\n", "    perdl=0.050,\n", "    rdrrtb=grass_rdrrtb,\n", "    rdrstb=grass_rdrstb,\n", "    swrd=3,\n", "    swdmi2rd=1,\n", "    rlwtb=grass_rlwtb,\n", "    wrtmax=3000.0,\n", "    swrdc=0,\n", "    rdctb=grass_rdctb,\n", ")\n", "\n", "grass_ox_stress = psp.components.crop.OxygenStress(\n", "    swoxygen=1, hlim1=0.0, hlim2u=1.0, hlim2l=-1.0, swwrtnonox=0\n", ")\n", "\n", "grass_drought_stress = psp.components.crop.DroughtStress(\n", "    swdrought=1,\n", "    swjarvis=4,\n", "    alphcrit=0.7,\n", "    hlim3h=-200.0,\n", "    hlim3l=-800.0,\n", "    hlim4=-8000.0,\n", "    adcrh=0.5,\n", "    adcrl=0.1,\n", ")\n", "\n", "grass_salt_stress = psp.components.crop.SaltStress(swsalinity=0)\n", "\n", "grass_interception = psp.components.crop.Interception(swinter=1, cofab=0.25)\n", "\n", "grass_co2 = psp.components.crop.CO2Correction(swco2=0)\n", "\n", "grass_dmmowtb = psp.components.crop.DMMOWTB.create({\n", "    \"DNR\": [120.0, 152.0, 182.0, 213.0, 366.0],\n", "    \"DMMOW\": [4700.0, 3700.0, 3200.0, 2700.0, 2700.0],\n", "})\n", "\n", "grass_dmmowdelay = psp.components.crop.DMMOWDELAY.create({\n", "    \"DMMOWDELAY\": [0.0, 2000.0, 4000.0],\n", "    \"DAYDELAY\": [2, 3, 4],\n", "})\n", "\n", "grass_management = psp.components.crop.GrasslandManagement(\n", "    seqgrazmow=[2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2],\n", "    swharvest=1,\n", "    swdmmow=2,\n", "    dmmowtb=grass_dmmowtb,\n", "    maxdaymow=42,\n", "    swlossmow=0,\n", "    mowrest=700.0,\n", "    dmmowdelay=grass_dmmowdelay,\n", "    swpotrelmf=1,\n", "    relmf=0.90,\n", ")\n", "\n", "grass_irrigation = psp.components.irrigation.ScheduledIrrigation(schedule=0)\n", "\n", "crpgrass = psp.components.crop.CropFile(\n", "    name=\"grassd\",\n", "    cropdev_settings=grass_settings,\n", "    oxygenstress=grass_ox_stress,\n", "    droughtstress=grass_drought_stress,\n", "    saltstress=grass_salt_stress,\n", "    interception=grass_interception,\n", "    co2correction=grass_co2,\n", "    grasslandmanagement=grass_management,\n", "    scheduledirrigation=grass_irrigation,\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Main Crop object\n", "\n", "the three CropFile objects defined above will be converted by pySWAP to .crp files and used in the simulation. They need to be added to the main Crop object defining the basic settings for crop simulation inside .swp model."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["croprotation = psp.components.crop.CROPROTATION.create({\n", "    \"CROPSTART\": [dt(2002, 5, 1), dt(2003, 5, 10), dt(2004, 1, 1)],\n", "    \"CROPEND\": [dt(2002, 10, 15), dt(2003, 9, 29), dt(2004, 12, 31)],\n", "    \"CROPFIL\": [\"'maizes'\", \"'potatod'\", \"'grassd'\"],\n", "    \"CROPTYPE\": [1, 2, 3],\n", "})\n", "\n", "crop = psp.components.crop.Crop(\n", "    swcrop=1,\n", "    rds=200.0,\n", "    croprotation=croprotation,\n", "    cropfiles={\"maizes\": crpmaize, \"potatod\": crppotato, \"grassd\": crpgrass},\n", ")\n", "\n", "ml.crop = crop"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Irrigation\n", "\n", "The irrigation section is relatively short, and if the fixed irrigation application is used, a dataframe of the irrigation events will be necessary."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["irrig_events = psp.components.irrigation.IRRIGEVENTS.create({\n", "    \"IRDATE\": [\"2002-01-05\"],\n", "    \"IRDEPTH\": [5.0],\n", "    \"IRCONC\": [1000.0],\n", "    \"IRTYPE\": [1],\n", "})\n", "\n", "fixed_irrigation = psp.components.irrigation.FixedIrrigation(\n", "    swirfix=1, swirgfil=0, irrigevents=irrig_events\n", ")\n", "\n", "ml.fixedirrigation = fixed_irrigation"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Soil-water parameters section\n", "\n", "This section is defining the soil water interaction parameters. "]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Soil moisture"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["soilmoisture = psp.components.soilwater.SoilMoisture(swinco=2, gwli=-75.0)\n", "ml.soilmoisture = soilmoisture"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Surface flow"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["surfaceflow = psp.components.soilwater.SurfaceFlow(\n", "    swpondmx=0, pondmx=0.2, rsro=0.5, rsroexp=1.0, swrunon=0\n", ")\n", "\n", "ml.surfaceflow = surfaceflow"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Evaporation"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["evaporation = psp.components.soilwater.Evaporation(\n", "    cfevappond=1.25, swcfbs=0, rsoil=30.0, swredu=1, cofredbl=0.35, rsigni=0.5\n", ")\n", "\n", "ml.evaporation = evaporation"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Soil profile"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["soil_profile = psp.components.soilwater.SOILPROFILE.create({\n", "    \"ISUBLAY\": [1, 2, 3, 4],\n", "    \"ISOILLAY\": [1, 1, 2, 2],\n", "    \"HSUBLAY\": [10.0, 20.0, 30.0, 140.0],\n", "    \"HCOMP\": [1.0, 5.0, 5.0, 10.0],\n", "    \"NCOMP\": [10, 4, 6, 14],\n", "})\n", "\n", "soil_hydraulic_functions = psp.components.soilwater.SOILHYDRFUNC.create({\n", "    \"ORES\": [0.01, 0.02],\n", "    \"OSAT\": [0.42, 0.38],\n", "    \"ALFA\": [0.0276, 0.0213],\n", "    \"NPAR\": [1.491, 1.951],\n", "    \"KSATFIT\": [12.52, 12.68],\n", "    \"LEXP\": [-1.060, 0.168],\n", "    \"ALFAW\": [0.0542, 0.0426],\n", "    \"H_ENPR\": [0.0, 0.0],\n", "    \"KSATEXM\": [12.52, 12.68],\n", "    \"BDENS\": [1315.0, 1315.0],\n", "})\n", "\n", "soilprofile = psp.components.soilwater.SoilProfile(\n", "    swsophy=0,\n", "    soilprofile=soil_profile,\n", "    swhyst=0,\n", "    tau=0.2,\n", "    soilhydrfunc=soil_hydraulic_functions,\n", "    swmacro=0,\n", ")\n", "\n", "ml.soilprofile = soilprofile"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Drainage"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dra = psp.components.drainage.DraFile(\n", "    dramet=2,\n", "    swdivd=1,\n", "    cofani=[1.0, 1.0],\n", "    swdislay=0,\n", "    lm2=11.0,\n", "    shape=0.8,\n", "    wetper=30.0,\n", "    zbotdr=-80.0,\n", "    entres=20.0,\n", "    ipos=2,\n", "    basegw=-200.0,\n", "    khtop=25.0,\n", ")\n", "\n", "drainage = psp.components.drainage.Drainage(swdra=1, drafile=dra)\n", "\n", "ml.lateraldrainage = drainage"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Bottom boundary conditions"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["bottom_boundary = psp.components.boundary.BottomBoundary(swbbcfile=0, swbotb=6)\n", "\n", "ml.bottomboundary = bottom_boundary"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["result = ml.run()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(result.yearly_summary)"]}], "metadata": {"kernelspec": {"display_name": "pyswap-U62T3krn-py3.11", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 2}