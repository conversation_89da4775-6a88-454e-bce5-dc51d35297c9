# mypy: disable-error-code="call-overload, misc, override"
# - model_string 上引发了 override 错误，因为这些方法不共享相同的签名。这不是一个优先修复的问题。
"""SWAP 模拟的灌溉设置。

类：
    IrgFile: 灌溉文件。
    FixedIrrigation: 固定灌溉设置。
    ScheduledIrrigation: 灌溉调度设置。

函数：
    irg_from_csv: 从 CSV 文件加载灌溉文件。
"""

from pathlib import Path as _Path
from typing import Literal as _Literal

from pydantic import (
    Field as _Field,
    PrivateAttr as _PrivateAttr,
)

from pyswap.components.tables import IRRIGEVENTS
from pyswap.core.basemodel import PySWAPBaseModel as _PySWAPBaseModel
from pyswap.core.defaults import FNAME_IN as _FNAME_IN
from pyswap.core.fields import (
    DayMonth as _DayMonth,
    String as _String,
    Table as _Table,
)
from pyswap.core.valueranges import YEARRANGE as _YEARRANGE
from pyswap.utils.mixins import (
    FileMixin as _FileMixin,
    SerializableMixin as _SerializableMixin,
    YAMLValidatorMixin as _YAMLValidatorMixin,
)

__all__ = ["FixedIrrigation", "ScheduledIrrigation", "IRRIGEVENTS"]


class FixedIrrigation(
    _PySWAPBaseModel, _SerializableMixin, _FileMixin, _YAMLValidatorMixin
):
    """ .swp 文件中的固定灌溉设置。

    属性：
        swirfix (Literal[0, 1]): 固定灌溉应用开关
        swirgfil (Literal[0, 1]): 独立文件固定灌溉应用开关
        irrigevents (Optional[Table]):
        irgfil (Optional[str]):
    """

    _extension = _PrivateAttr(default="irg")

    swirfix: _Literal[0, 1] | None = None
    swirgfil: _Literal[0, 1] | None = None
    irgfil: _String = _Field(default=_FNAME_IN, frozen=True)
    irrigevents: _Table | None = None

    def model_string(self, **kwargs) -> str:
        """覆盖 model_string 以处理可选文件生成。

        如果 swirgfil 设置为 1，则返回完整部分；否则，irrigevents 将从字符串中排除并保存到单独的 .irg 文件中。
        """
        if self.swirgfil == 1:
            return super().model_string(exclude={"irrigevents"}, **kwargs)
        else:
            return super().model_string()

    @property
    def irg(self):
        return super().model_string(include={"irrigevents"})

    def write_irg(self, path: _Path):
        """将灌溉数据写入 .irg 文件。

        此方法仅在 swirgfil 属性设置为 1 时可用。

        参数：
            path (Path): .irg 文件将保存到的目录路径。
        """
        if self.swirgfil != 1:
            msg = "Irrigation data are not set to be written to a .irg file."
            raise ValueError(msg)

        self.save_file(string=self.irg, fname=self.irgfil, path=path)


class ScheduledIrrigation(_PySWAPBaseModel, _SerializableMixin, _YAMLValidatorMixin):
    """ .crp 文件中的灌溉调度设置。

    属性：
        schedule (Literal[0, 1]): 应用灌溉调度开关
        startirr (str): 指定灌溉调度开始的日期和月份
        endirr (str): 指定灌溉调度结束的日期和月份
        cirrs (float): 灌溉水的溶质浓度
        isuas (int): 灌溉方法类型开关

            * 0 - 喷灌
            * 1 - 地表灌溉

        tcs (int): 选择以下时间标准选项之一

            * 1 - 实际/潜在蒸腾比
            * 2 - 易利用水耗尽
            * 3 - 总利用水耗尽
            * 4 - 绝对水量耗尽
            * 6 - 固定每周灌溉
            * 7 - 压头
            * 8 - 含水量

        phFieldCapacity (float): 田间持水量的土壤水压头
        irgthreshold (Optional[float]): 每周灌溉的阈值
        dcrit (Optional[float]): 传感器深度
        swcirrthres (Optional[bool]): 过度灌溉开关
        cirrthres (Optional[float]): 发生过度灌溉的阈值盐度浓度
        perirrsurp (Optional[float]): 通常计划的灌溉深度的过度灌溉
        tcsfix (Optional[int]): 灌溉应用之间最小时间间隔开关
        irgdayfix (Optional[int]): 灌溉应用之间最小天数
        phormc (Optional[int]): 使用压头或含水量的开关

            * 0 - 压头
            * 1 - 含水量

        dvs_tc1 (Optional[Table]):
        dvs_tc2 (Optional[Table]):
        dvs_tc3 (Optional[Table]):
        dvs_tc4 (Optional[Table]):
        dvs_tc5 (Optional[Table]):
    """

    schedule: _Literal[0, 1] | None = None
    startirr: _DayMonth | None = None
    endirr: _DayMonth | None = None
    cirrs: float | None = _Field(default=None, ge=0.0, le=100.0)
    isuas: _Literal[0, 1] | None = None
    tcs: _Literal[1, 2, 3, 4, 6, 7, 8] | None = None

    phfieldcapacity: float | None = _Field(default=None, ge=-1000.0, le=0.0)
    irgthreshold: float | None = _Field(default=None, ge=0.0, le=20.0)
    dcrit: float | None = _Field(default=None, ge=-100.0, le=0.0)
    swcirrthres: _Literal[0, 1] | None = None
    cirrthres: float | None = _Field(default=None, ge=0.0, le=100.0)
    perirrsurp: float | None = _Field(default=None, ge=0.0, le=100.0)
    tcsfix: _Literal[0, 1] | None = None
    irgdayfix: int | None = _Field(default=None, **_YEARRANGE)
    dcs: _Literal[0, 1] | None = None
    dcslim: _Literal[0, 1] | None = None
    irgdepmin: float | None = _Field(default=None, ge=0.0, le=100.0)
    irgdepmax: float | None = _Field(default=None, ge=0.0, le=1.0e7)
    tc1tb: _Table | None = None
    tc2tb: _Table | None = None
    tc3tb: _Table | None = None
    tc4tb: _Table | None = None
    tc7tb: _Table | None = None
    tc8tb: _Table | None = None
    dc1tb: _Table | None = None
    dc2tb: _Table | None = None
