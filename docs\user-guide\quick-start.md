# 快速入门

如果您已经对 SWAP 和 pySWAP 有很多了解，请直接跳到安装、运行测试用例和设置您自己的模型。

## 安装

pySWAP 可以通过 pip 简单安装。建议创建单独的虚拟环境。

```sh
pip install pyswap
```

## 运行测试用例

安装后，您可以通过运行测试用例来测试一切是否正常：

```py
pyswap-py3.11vscode ➜ /workspaces/pySWAP (dev) $ python
Python 3.11.11 (main, Dec  4 2024, 20:36:16) [GCC 10.2.1 20210110] on linux
Type "help", "copyright", "credits" or "license" for more information.
>>> from pyswap import testcase
>>> hupselbrook = testcase.get("hupselbrook")
>>> result = hupselbrook.run()
Warning from module Readswap : simulation with additonal Ksat value (Ksatexm)
>>> result.yearly_summary
             RAIN  IRRIG   INTERC  RUNOFF      EPOT      EACT  DRAINAGE  QBOTTOM         GWL      TPOT      TACT    DSTOR
DATETIME
2002-12-31  84.18    0.5  3.74188     0.0  33.10679  16.68715  22.11357      0.0 -1107.65824  38.71198  38.17328  3.96418
2003-12-31  71.98    0.0  2.05788     0.0  35.99241  17.17961  26.44815      0.0 -1154.37603  29.41787  29.21504 -2.92064
2004-12-31  80.55    0.0  4.91521     0.0  29.89227  17.88916  24.76607      0.0 -1036.76085  32.57266  32.56927  0.41030
```

## 使用 pyswap CLI 工具启动您的项目

## 开启探索模式

您可以在终端中试用模型和结果，或者前往[教程部分](/tutorials/)进行实践练习，或者继续阅读用户指南的[下一页](/user-guide/ascii-vs-classes/)。
