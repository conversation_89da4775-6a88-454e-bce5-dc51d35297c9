{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Load classic SWAP model\n", "\n", "This notebook demonstrates how to load a SWAP model into pyswap classes from standard ASCII files.\n", "\n", "We begin, like always, by defining metadata. Then we can use load_swp to get the scaffolding of the model with all parameters from the initial .swp file loaded.\n", "\n", "In the grass growth example, which we use here, bottom boundary conditions are defined in a separate .bbc file. In pyswap, all the parameters of bottom boundary are in the same class. load_bbc function therefore will either update an instance of BottomBoundary you provide (like we do below), or will return a new one with just the parameters set in the .bbc file. The first option is recommended."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pyswap as psp"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from pyswap import load_bbc, load_crp, load_dra, load_swp, testcase\n", "\n", "meta = psp.components.Metadata(\n", "    author=\"<PERSON>\",\n", "    institution=\"University of Somewhere\",\n", "    email=\"<EMAIL>\",\n", "    project=\"pySWAP test - hupselbrook\",\n", "    swap_ver=\"4.2\",\n", ")\n", "\n", "ml = load_swp(testcase.get_path(\"grassgrowth\", \"swp\"), meta)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(ml.swp)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ml.bottomboundary = load_bbc(testcase.get_path(\"grassgrowth\", \"bbc\"), ml.bottomboundary)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(ml.swp)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Crop files are provided to the Crop instance as a dictionary. Below is one way to load crp file"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ml.crop.cropfiles = {\n", "    \"grassd\": load_crp(\n", "        testcase.get_path(\"grassgrowth\", \"grassd\"), crptype=\"grass\", name=\"grassd\"\n", "    )\n", "}"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(ml.crop.cropfiles.get(\"grassd\").crp)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ml.lateraldrainage.drafile = load_dra(testcase.get_path(\"grassgrowth\", \"dra\"))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ml.meteorology.metfile = psp.components.meteorology.metfile_from_csv(\n", "    \"260.met\", testcase.get_path(\"grassgrowth\", \"met\")\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ml.get_inputs()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["result = ml.run(silence_warnings=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["result.yearly_summary"]}], "metadata": {"kernelspec": {"display_name": "pyswap-U62T3krn-py3.11", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 2}