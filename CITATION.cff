# 此 CITATION.cff 文件由 cffinit 生成。
# 立即访问 https://bit.ly/cffinit 生成您的文件！

cff-version: 1.2.0
title: "pyswap: Python wrapper for SWAP hydrological model."
message: >-
  如果您使用此软件，请使用此文件中的元数据引用它。
type: software
authors:
  - given-names: <PERSON><PERSON><PERSON>
    family-names: <PERSON>awadzki
    email: <EMAIL>
    affiliation: Vrije Universiteit Brussel
    orcid: "https://orcid.org/0000-0001-9524-4208"
identifiers:
  - type: doi
    value: 10.5281/zenodo.14884488
    description: 最新 pyswap 版本
repository-code: "https://github.com/zawadzkim/pySWAP"
url: "https://zawadzkim.github.io/pySWAP/"
abstract: >-
  pySWAP 是 SWAP 水文模型的 Python 包装器（而非 Python 实现）。
  它简化了输入文件的创建、SWAP 模型的执行以及结果的分析和可视化。
  用户可以在 Jupyter Notebook 中设置和记录他们的模型，从而提高透明度、
  协作性，并促进社区支持的调试。
keywords:
  - vadose zone
  - hydrology
  - SWAP model
  - 1D model
license: MIT
date-released: "2025-02-17"
