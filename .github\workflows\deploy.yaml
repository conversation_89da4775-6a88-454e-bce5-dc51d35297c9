name: Build GitHub Pages
on:
  push:
    branches:
      - main
      - master
    paths:
      - "docs/**"

permissions:
  contents: write
  pages: write
  id-token: write

jobs:
  build_mkdocs:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          submodules: "recursive"
          fetch-depth: 0

      - name: Setup Python
        uses: actions/setup-python@v5
        with:
          python-version: 3.11
      - name: Install Poetry
        run: curl -sSL https://install.python-poetry.org | python3 -

      - name: Install dependencies
        run: poetry install

      - name: Build MkDocs site
        run: poetry run mkdocs gh-deploy --config-file mkdocs.yml --force

  deploy_mkdocs:
    needs: build_mkdocs
    environment:
      name: github-pages
      url: ${{ steps.deployment.outputs.page_url }}
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          submodules: "recursive"
          ref: gh-pages
      - name: Setup Pages
        uses: actions/configure-pages@v5
      - name: Upload artifact
        uses: actions/upload-pages-artifact@v3
        with:
          path: "."
      - name: Deploy to GitHub Pages
        id: deployment
        uses: actions/deploy-pages@v4
